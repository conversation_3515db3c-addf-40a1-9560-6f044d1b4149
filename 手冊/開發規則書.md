# 開發規則書

## 1. 版本控制

- **工具：** Git
- **倉庫：** 使用本地 Git 倉庫進行版本控制。
- **分支策略：**
    - `main` (或 `master`): 用於存放穩定、可發布的版本。
    - `develop`: 主要開發分支，所有功能開發在此分支進行或從此分支創建。
    - `feature/<feature-name>`: 用於開發新功能，完成後合併回 `develop`。
    - `bugfix/<issue-id>`: 用於修復 `develop` 分支的 bug。
    - `hotfix/<issue-id>`: 用於修復 `main` 分支的緊急 bug，完成後需合併回 `main` 和 `develop`。
- **提交信息：**
    - 格式：`<類型>(<範圍>): <主題>`
    - 類型：`feat` (新功能), `fix` (錯誤修復), `docs` (文件修改), `style` (格式調整), `refactor` (重構), `test` (測試), `chore` (構建、輔助工具)
    - 範圍：可選，指明影響的模塊 (例如 `ui`, `login`, `settings`)
    - 主題：簡潔描述修改內容。
    - 示例：`feat(ui): Add window pinning feature`
- **工作流程：**
    1. 從 `develop` 分支創建新的 `feature` 或 `bugfix` 分支。
    2. 在本地分支上進行開發和提交。
    3. 定期將 `develop` 分支的更新合併到本地分支 (`git pull origin develop`)。
    4. 完成開發後，將本地分支推送到遠程 (如果使用共享庫)，或直接準備合併。
    5. 確保程式碼符合規範并通过測試 (如果設置)。
    6. 將 `feature` 或 `bugfix` 分支合併回 `develop` 分支。
    7. (可選) 刪除已合併的本地和遠程分支。

## 2. 編碼規範

- **語言：** Python 3.x
- **風格指南：** 遵循 [PEP 8 -- Style Guide for Python Code](https://peps.python.org/pep-0008/)。
    - 使用 4 個空格縮進。
    - 行長度限制在 100-120 字符之間。
    - 使用有意義的變數名和函數名 (蛇形命名法 `snake_case` for functions and variables, `PascalCase` for classes)。
    - 添加適當的 Docstrings 解釋類和函數的功能、參數和返回值。
- **註釋：**
    - 對複雜或不明顯的程式碼段添加註釋。
    - 避免冗餘或顯而易見的註釋。
    - 及時更新或刪除過時的註釋。
- **錯誤處理：**
    - 使用 `try...except` 處理可能發生的異常。
    - 提供清晰的錯誤信息或日誌記錄。
    - 避免捕獲過於寬泛的異常 (如 `except Exception:`)，除非有充分理由。
- **依賴管理：**
    - 使用 `requirements.txt` 文件管理項目依賴。
    - 定期更新依賴版本，並測試兼容性。
    - 優先使用虛擬環境 (`venv`) 隔離項目依賴。

## 3. UI/UX 設計原則

- **一致性：** 保持界面元素 (按鈕、標籤、顏色、字體) 的風格和行為一致。
- **響應性：** 確保界面在不同尺寸設定下都能良好顯示和操作。
- **清晰度：** 使用清晰的標籤和提示信息，避免歧義。
- **用戶反饋：** 對用戶操作提供及時的視覺反饋 (如按鈕點擊效果、加載提示)。
- **簡潔性：** 避免界面過於擁擠，突出核心功能。

## 4. 文件結構

- 保持清晰的模塊化文件結構，將相關功能組織在同一個 Python 文件或目錄中。
- UI 相關常數應集中存放在 `*_constants.py` 文件中。
- 靜態資源 (如圖標) 應存放在指定目錄 (如 `icons/`)。
- 文檔應存放在 `手冊/` 目錄下。

## 5. 數據庫交互

- (待定) 數據庫操作應封裝在獨立的數據訪問層 (DAL) 或模塊中。
- 避免在 UI 程式碼中直接編寫 SQL 語句。
- 使用參數化查詢防止 SQL 注入。
- 盡可能減少不必要的數據庫連接和查詢。
- (強調) 盡可能直接跨表、聯表查詢獲取所需數據，減少同步多表數據的複雜性和潛在問題。

## 6. 文檔

- **README.md:** 提供項目概述、安裝指南、基本用法。
- **手冊/README.md:** 提供 `手冊` 目錄下各文件的說明，以及更詳細的項目信息 (如業務邏輯、數據結構、API 等)。
- **手冊/工作日誌(YYYY-MM-DD).md:** 記錄開發過程、解決的問題和決策。
- **手冊/項目需求書.md:** 定義功能需求和界面邏輯。
- **手冊/開發規則書.md:** (本文檔) 定義開發規範。
- **手冊/DB數據結構表.md:** 描述數據庫表結構。
- **手冊/網站樹狀結構.md:** (桌面應用適用性待定，可改為模塊/窗口結構圖) 描述應用結構。
- **手冊/後台API介接說明書.md:** (如果需要與後端交互) 定義 API 接口。
- **手冊/開發進度報告.md:** 追蹤開發計劃和進度。
- **程式碼內文檔:** 使用 Docstrings 和註釋解釋程式碼。

## 7. 錯誤處理與日誌

- 對於用戶操作錯誤，應提供清晰的提示信息 (如 QMessageBox)。
- 對於內部錯誤或異常，應記錄詳細的日誌信息 (時間、錯誤類型、堆棧跟踪、相關上下文)，以便於調試。
- (待定) 可考慮使用 Python 的 `logging` 模塊進行日誌記錄。

## 8. 測試

- (待定) 鼓勵編寫單元測試 (`unittest` 或 `pytest`) 來驗證核心邏輯。
- (待定) 進行界面測試，確保 UI 在不同尺寸和交互下的表現符合預期。 