# 模塊/窗口結構圖

本文檔描述了「出貨記錄系統」桌面應用程式的主要模塊和窗口結構。

```mermaid
graph TD
    A[main.py] --> B(LoginWindow / login.py);
    B --> C{驗證成功};
    C --> D(MainWindow / main_window.py);
    D --> E[導航欄];
    D --> F[底部區域];
    D --> G[標題欄];

    E --> H(出貨鏡頭按鈕);
    E --> I(進行出貨按鈕);
    E --> J(服務訂閱按鈕);
    E --> K(系統設定按鈕);

    H -- 點擊 --> L(ShipmentWindow / content_windows.py);
    I -- 點擊 --> M(ProcessShipmentWindow / content_windows.py);
    J -- 點擊 --> N(SubscriptionWindow / content_windows.py);
    K -- 點擊 --> O(SettingsWindow / settings_window.py);

    L --> P[BaseContentWindow];
    M --> P;
    N --> P;
    O --> P;

    F --> Q(訂閱標籤);
    F --> R(登出按鈕);

    O -- 觸發 --> D; # 設定窗口修改主窗口尺寸

    subgraph 主窗口區域
        E
        F
        G
    end

    subgraph 功能窗口 (可多開, 基於BaseContentWindow)
        L
        M
        N
        O
    end
```

## 結構說明

1.  **`main.py`**: 應用程式入口點，負責初始化 QApplication 和顯示登入窗口 (`LoginWindow`)。
2.  **`login.py` (`LoginWindow`)**: 處理用戶登入驗證。成功後，實例化並顯示主窗口 (`MainWindow`)。
3.  **`main_window.py` (`MainWindow`)**: 應用程式主框架。
    *   包含自定義標題欄 (支持拖動、釘選)。
    *   包含左側導航欄，有四個主要功能按鈕。
    *   包含底部區域，顯示訂閱信息和登出按鈕。
    *   負責管理和打開/關閉各個功能子窗口 (`open_window` 方法)。
    *   負責應用尺寸/縮放設置 (`apply_size_setting`, `apply_scale_setting`)。
    *   從 `main_window_constants.py` 讀取 UI 尺寸和樣式常數。
4.  **`content_windows.py`**: 定義各個功能窗口的內容。
    *   **`BaseContentWindow`**: 所有功能窗口的基類，提供標準的自定義標題欄 (含釘選、關閉按鈕) 和窗口拖動功能。
    *   **`ShipmentWindow`**: 「出貨鏡頭」窗口。
    *   **`ProcessShipmentWindow`**: 「進行出貨」窗口。
    *   **`SubscriptionWindow`**: 「服務訂閱」窗口。
5.  **`settings_window.py` (`SettingsWindow`)**: 「系統設定」窗口。
    *   允許用戶更改主窗口的尺寸設定。
    *   通過調用 `MainWindow` 的 `apply_size_setting` 方法來觸發 UI 更新。
6.  **`main_window_constants.py`**: 存儲主窗口及導航元素的尺寸、間距、字體大小等常數。
7.  **`settings.json`**: 存儲持久化的用戶設置，如窗口大小偏好、上次窗口位置。
8.  **`icons/`**: 存儲界面使用的 SVG 圖標。 