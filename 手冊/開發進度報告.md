# 開發進度報告

## 1. 事前規劃計畫 (初步)

**版本 0.1 (基礎框架與 UI 調整)**

- [x] 初始化專案結構
- [x] 實現登入窗口基礎 UI (`login.py`)
- [x] 實現主窗口基礎 UI 與導航 (`main_window.py`)
- [x] 實現功能窗口基類 (`BaseContentWindow` in `content_windows.py`)
- [x] 實現各功能窗口的基礎 UI 布局 (`ShipmentWindow`, `ProcessShipmentWindow`, `SubscriptionWindow` in `content_windows.py`)
- [x] 實現系統設定窗口 UI (`settings_window.py`)
- [x] 實現 UI 尺寸動態調整功能 (基於 `main_window_constants.py`)
- [x] 添加窗口拖動和釘選功能
- [x] 添加圖標資源 (`icons/`)
- [x] 添加配置文件 (`settings.json`) 保存窗口位置和尺寸偏好
- [x] 建立初步的項目文檔 (`手冊/` 目錄)
    - [x] 工作日誌
    - [x] 項目需求書
    - [x] 開發規則書
    - [x] DB數據結構表 (模板)
    - [x] 模塊/窗口結構圖
    - [x] 後台API介接說明書 (模板)
    - [x] 開發進度報告 (本文檔)
    - [ ] 手冊 README
- [x] 根據用戶反饋調整 UI 細節 (尺寸、字體大小、按鈕高度)
- [ ] 初始化本地 Git 倉庫

**版本 0.2 (核心功能 - 進行出貨)**

- [ ] 設計並實現數據庫結構 (選擇 SQLite 或其他)
- [ ] 實現數據庫連接和基礎 CRUD 操作封裝
- [ ] `ProcessShipmentWindow`: 實現條碼掃描邏輯 (模擬或接入真實設備)
- [ ] `ProcessShipmentWindow`: 實現產品與包裹匹配邏輯
- [ ] `ProcessShipmentWindow`: 實現出貨信息寫入數據庫 (`shipments`, `packages` 狀態更新)
- [ ] `ProcessShipmentWindow`: 實現手動輸入功能
- [ ] `ProcessShipmentWindow`: 實現強制出貨功能
- [ ] 添加必要的錯誤處理和用戶提示

**版本 0.3 (核心功能 - 出貨鏡頭)**

- [ ] `ShipmentWindow`: 實現攝像頭畫面顯示 (接入真實設備或模擬)
- [ ] `ShipmentWindow`: 實現錄製功能 (啟動/停止)
- [ ] `ShipmentWindow`: 實現視頻文件保存
- [ ] `ShipmentWindow`: 實現記錄列表展示 (從數據庫讀取 `shipment_records`)
- [ ] `ShipmentWindow`: 實現記錄播放和刪除功能
- [ ] `SettingsWindow`: 添加攝像頭相關設定 (選擇設備、分組等)

**版本 0.4 (用戶與訂閱)**

- [ ] 實現真實用戶登入驗證 (密碼哈希)
- [ ] (可選) 實現後端 API 對接，用於訂閱驗證和唯一設備登入
- [ ] `SubscriptionWindow`: 實現訂閱狀態顯示 (從數據庫或 API 讀取)
- [ ] (可選) 實現續訂/購買流程

**版本 1.0 (穩定版)**

- [ ] 添加單元測試和集成測試
- [ ] 代碼審查和重構
- [ ] 完善文檔
- [ ] 創建安裝包

## 2. 當前進度 (截至 YYYY-MM-DD HH:MM)

- **已完成：**
    - 版本 0.1 的大部分任務，主要集中在 UI 框架搭建和樣式調整。
    - 根據用戶多次反饋調整了不同尺寸下的字體大小和按鈕高度。
    - 創建了初步的項目文檔結構和內容。
- **進行中：**
    - 完善項目文檔 (`手冊/README.md`)。
    - 初始化本地 Git 倉庫。
- **下一步計劃：**
    - 開始版本 0.2 的開發，重點是數據庫設計與實現，以及「進行出貨」窗口的核心邏輯。 