# 手冊 README

本文檔為「出貨記錄系統」項目 `手冊` 目錄的說明文件，旨在幫助開發者（包括其他 AI 協作夥伴）快速理解項目結構、設計思路、開發規範和當前狀態。

## 1. 文件說明

本目錄包含以下主要文檔：

- **`工作日誌(YYYY-MM-DD).md`**: 按日期記錄詳細的開發過程、遇到的問題、解決方案和重要決策。請開發者在進行重要修改後及時更新。
- **`項目需求書.md`**: 定義系統的核心功能、用戶界面設計和交互邏輯。描述了每個窗口（列表頁、編輯彈窗等）的功能和涉及的數據庫表（目前為預計）。
- **`開發規則書.md`**: 規定了項目開發過程中應遵循的規範，包括版本控制 (Git)、編碼風格 (PEP 8)、UI/UX 設計原則、文件結構、數據庫交互原則、文檔要求、錯誤處理和測試策略。
- **`DB數據結構表.md`**: （目前為模板）詳細描述了未來數據庫中每個表的結構，包括欄位名稱、數據類型、約束和用途說明。當數據庫實現後，需保持此文件與實際結構一致。
- **`網站樹狀結構.md`**: (實際為模塊/窗口結構圖) 使用 Mermaid 圖表展示了應用程式的模塊組成和窗口之間的調用關係。
- **`後台API介接說明書.md`**: （目前為模板）定義了桌面應用與後端服務器交互的 API 接口規範，包括 Endpoint、請求/響應格式和認證方式。
- **`開發進度報告.md`**: 包含項目的事前開發計劃（按版本劃分里程碑）和當前的開發進度摘要。定期更新以反映實際進展。
- **`README.md`**: (本文檔) 提供本目錄的概覽和指引。

## 2. 核心業務邏輯概述

1.  **啟動與登入**: 應用程式啟動後首先顯示登入窗口。用戶輸入憑證後，系統進行驗證（目前為模擬，未來可能涉及後端 API 交互、MAC 地址檢查、訂閱狀態檢查）。
2.  **主界面導航**: 登入成功後進入主窗口，左側提供固定導航欄，允許用戶打開四個主要功能窗口：出貨鏡頭、進行出貨、服務訂閱、系統設定。
3.  **多窗口管理**: 功能窗口可以獨立打開、關閉和釘選。主窗口管理這些子窗口的實例和顯示位置。
4.  **出貨流程 (核心)**:
    *   **進行出貨窗口**: 用於掃描或手動輸入產品條碼和包裹條碼，系統進行匹配驗證，成功後記錄一條出貨信息到數據庫 (`shipments` 表)。
    *   **出貨鏡頭窗口**: 用於查看攝像頭畫面，並可啟動/停止錄製出貨過程。錄製完成後，視頻文件路徑等信息將存儲到數據庫 (`shipment_records` 表)，並可能與對應的 `shipments` 記錄關聯。
5.  **訂閱管理**: 用戶可以查看當前服務訂閱狀態和到期日（未來從數據庫或 API 獲取）。
6.  **系統設定**: 用戶可以調整應用程式的外觀，主要是界面元素的大小比例。設定會保存到 `settings.json` 並在下次啟動時加載。
7.  **配置持久化**: 應用程式會將用戶的尺寸偏好和主窗口的最後位置保存在 `settings.json` 文件中。

## 3. 數據管理原則

- **UI 與數據分離**: 界面邏輯 (PySide6 Widgets) 應與數據處理邏輯分離。數據庫操作應封裝在獨立的模塊或類中。
- **減少同步**: 設計數據庫查詢時，盡量使用 SQL JOIN 操作直接獲取關聯數據，避免讀取多個表後在 Python 中進行數據合併，以減少複雜度和潛在錯誤。
- **數據一致性**: 對於狀態變更（如包裹狀態、訂閱狀態），應確保數據庫更新的原子性和一致性。

## 4. 開發者須知

- 在進行任何修改前，請確保理解相關模塊的功能和 `開發規則書.md` 中的規範。
- 完成重要功能或修復後，請更新 `工作日誌` 和 `開發進度報告`。
- 如果修改涉及數據庫結構或 API 接口，請務必同步更新 `DB數據結構表.md` 或 `後台API介接說明書.md`。
- 定期使用 Git 提交更改，並編寫清晰的提交信息。 