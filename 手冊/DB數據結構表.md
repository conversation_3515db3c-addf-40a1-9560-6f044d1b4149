# DB數據結構表

## 概述

本文檔定義了「出貨記錄系統」未來可能使用的數據庫表結構。目前系統尚未使用數據庫，以下為初步設計。

**數據庫類型：** (待定，建議 SQLite 或 MySQL/PostgreSQL)

## 表結構

### 1. 用戶表 (`users`)

存儲用戶登入信息。

| 欄位名稱        | 數據類型      | 約束        | 描述                     |
| --------------- | ------------- | ----------- | ------------------------ |
| `user_id`       | INTEGER       | PRIMARY KEY | 用戶唯一標識符           |
| `username`      | VARCHAR(50)   | UNIQUE, NOT NULL | 用戶名                   |
| `password_hash` | VARCHAR(255)  | NOT NULL    | 加密後的密碼             |
| `mac_address`   | VARCHAR(17)   |             | 用戶設備的 MAC 地址 (可選) |
| `is_active`     | BOOLEAN       | NOT NULL, DEFAULT TRUE | 帳號是否啟用             |
| `last_login`    | DATETIME      |             | 上次登入時間             |
| `created_at`    | DATETIME      | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 創建時間                 |

### 2. 訂閱表 (`subscriptions`)

存儲用戶的服務訂閱信息。

| 欄位名稱           | 數據類型    | 約束        | 描述                     |
| ------------------ | ----------- | ----------- | ------------------------ |
| `subscription_id`  | INTEGER     | PRIMARY KEY | 訂閱唯一標識符           |
| `user_id`          | INTEGER     | NOT NULL, FOREIGN KEY (`users.user_id`) | 關聯的用戶 ID            |
| `plan_type`        | VARCHAR(50) | NOT NULL    | 訂閱方案類型 (例如 'Pro') |
| `status`           | VARCHAR(20) | NOT NULL    | 訂閱狀態 ('active', 'expired', 'cancelled') |
| `start_date`       | DATE        | NOT NULL    | 訂閱開始日期             |
| `expiry_date`      | DATE        | NOT NULL    | 訂閱到期日期             |
| `auto_renew`       | BOOLEAN     | NOT NULL, DEFAULT FALSE | 是否自動續訂             |
| `created_at`       | DATETIME    | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 創建時間                 |
| `updated_at`       | DATETIME    |             | 最後更新時間             |

### 3. 出貨記錄表 (`shipment_records`)

存儲每一次出貨操作的記錄信息，包含視頻記錄關聯。

| 欄位名稱            | 數據類型      | 約束        | 描述                         |
| ------------------- | ------------- | ----------- | ---------------------------- |
| `record_id`         | INTEGER       | PRIMARY KEY | 記錄唯一標識符               |
| `user_id`           | INTEGER       | NOT NULL, FOREIGN KEY (`users.user_id`) | 操作用戶 ID                  |
| `shipment_id`       | INTEGER       | FOREIGN KEY (`shipments.shipment_id`) | 關聯的出貨 ID (如果適用)     |
| `timestamp`         | DATETIME      | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 記錄時間戳                   |
| `status`            | VARCHAR(20)   | NOT NULL    | 記錄狀態 ('recording', 'completed', 'error') |
| `video_file_path` | VARCHAR(255)  |             | 錄製的視頻文件路徑           |
| `duration_seconds`  | INTEGER       |             | 視頻時長 (秒)                |
| `camera_group`      | VARCHAR(50)   |             | 使用的攝像頭分組 (例如 'A組') |
| `notes`             | TEXT          |             | 備註                         |

### 4. 攝像頭設置表 (`camera_settings`)

存儲系統中可用的攝像頭信息。

| 欄位名稱       | 數據類型      | 約束        | 描述                     |
| -------------- | ------------- | ----------- | ------------------------ |
| `camera_id`    | INTEGER       | PRIMARY KEY | 攝像頭唯一標識符         |
| `name`         | VARCHAR(100)  | NOT NULL    | 攝像頭名稱 (用戶自定義)  |
| `source`       | VARCHAR(255)  | NOT NULL    | 攝像頭來源 (URL, 設備索引) |
| `group_name`   | VARCHAR(50)   |             | 所屬分組 (例如 'A組')    |
| `is_active`    | BOOLEAN       | NOT NULL, DEFAULT TRUE | 是否啟用                 |
| `resolution`   | VARCHAR(20)   |             | 分辨率 (例如 '1920x1080') |
| `created_at`   | DATETIME      | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 創建時間                 |

### 5. 產品表 (`products`)

存儲產品信息。

| 欄位名稱      | 數據類型      | 約束        | 描述             |
| ------------- | ------------- | ----------- | ---------------- |
| `product_id`  | INTEGER       | PRIMARY KEY | 產品唯一標識符   |
| `barcode`     | VARCHAR(100)  | UNIQUE, NOT NULL | 產品條碼/QR碼    |
| `name`        | VARCHAR(255)  | NOT NULL    | 產品名稱         |
| `specification` | TEXT          |             | 產品規格描述     |
| `created_at`  | DATETIME      | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 創建時間         |

### 6. 包裹表 (`packages`)

存儲包裹信息。

| 欄位名稱      | 數據類型      | 約束        | 描述                     |
| ------------- | ------------- | ----------- | ------------------------ |
| `package_id`  | INTEGER       | PRIMARY KEY | 包裹唯一標識符           |
| `barcode`     | VARCHAR(100)  | UNIQUE, NOT NULL | 包裹條碼/運單號        |
| `status`      | VARCHAR(20)   | NOT NULL    | 包裹狀態 ('pending', 'shipped', 'delivered') |
| `created_at`  | DATETIME      | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 創建時間                 |

### 7. 出貨信息表 (`shipments`)

記錄產品與包裹的出貨匹配信息。

| 欄位名稱       | 數據類型     | 約束        | 描述                           |
| -------------- | ------------ | ----------- | ------------------------------ |
| `shipment_id`  | INTEGER      | PRIMARY KEY | 出貨唯一標識符                 |
| `product_id`   | INTEGER      | NOT NULL, FOREIGN KEY (`products.product_id`) | 關聯的產品 ID                  |
| `package_id`   | INTEGER      | NOT NULL, FOREIGN KEY (`packages.package_id`) | 關聯的包裹 ID                  |
| `user_id`      | INTEGER      | NOT NULL, FOREIGN KEY (`users.user_id`) | 操作用戶 ID                    |
| `shipment_time`| DATETIME     | NOT NULL, DEFAULT CURRENT_TIMESTAMP | 出貨操作時間                   |
| `status`       | VARCHAR(20)  | NOT NULL    | 出貨狀態 ('success', 'manual', 'error') |
| `notes`        | TEXT         |             | 備註 (例如 手動出貨原因)       | 