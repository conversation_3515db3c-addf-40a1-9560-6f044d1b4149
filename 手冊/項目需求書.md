# 項目需求書

## 1. 概述

本文件定義了「出貨記錄系統」桌面應用程式的功能需求、用戶界面邏輯和涉及的數據庫結構。

## 2. 主要功能

- **用戶登入：** 驗證用戶身份，未來可能包含訂閱狀態檢查和唯一設備登入限制。
- **出貨記錄查看：** 提供界面展示歷史出貨記錄（列表形式）。
- **進行新出貨：** 核心功能，用於記錄新的出貨操作，可能包含條碼掃描、手動輸入、攝像頭記錄等。
- **服務訂閱管理：** 顯示和管理用戶的服務訂閱狀態及到期日。
- **系統設定：** 允許用戶調整應用程式的外觀，如界面尺寸。

## 3. 界面邏輯

### 3.1 登入窗口 (`login.py`)

- **界面元素：**
    - 應用程式標題
    - 用戶名輸入框 (QLineEdit)
    - 密碼輸入框 (QLineEdit, 密碼模式)
    - 登入按鈕 (QPushButton)
- **交互邏輯：**
    - 輸入用戶名和密碼。
    - 點擊「登入」按鈕觸發 `login` 方法。
    - `login` 方法：
        - 獲取輸入的用戶名和密碼。
        - 進行基本驗證 (非空)。
        - (已實現) 獲取 MAC 地址和系統資訊 (`get_mac_address`)。
        - (模擬) 調用 `verify_subscription` 進行訂閱驗證 (未來需連接後端 API)。
        - 驗證成功後，創建並顯示主窗口 (`MainWindow`)，關閉登入窗口。
        - 驗證失敗，顯示錯誤提示 (QMessageBox)。
- **涉及數據：**
    - (未來) `users` 表 (用戶名, 密碼哈希, MAC地址, 登入狀態)
    - (未來) `subscriptions` 表 (用戶ID, 訂閱狀態, 到期日)

### 3.2 主窗口 (`main_window.py`)

- **界面元素：**
    - **標題欄 (自定義 Frame)：**
        - 釘選按鈕 (QPushButton#pinBtn)
        - 窗口標題 (QLabel#headerTitle)
    - **導航欄 (QWidget#nav_container)：**
        - 出貨鏡頭按鈕 (QFrame > QLabel#icon_label, QLabel#button_text)
        - 進行出貨按鈕 (同上)
        - 服務訂閱按鈕 (同上)
        - 系統設定按鈕 (同上)
    - **底部區域 (QFrame#bottom_container)：**
        - 訂閱狀態/到期日標籤 (QLabel#subscription)
        - 登出按鈕 (QPushButton#logout)
- **交互邏輯：**
    - **窗口拖動：** 按住標題欄可拖動窗口 (`mousePressEvent`, `mouseMoveEvent`, `mouseReleaseEvent`)。
    - **窗口釘選：** 點擊釘選按鈕切換置頂狀態 (`toggle_pin`)。
    - **導航：** 點擊導航按鈕觸發 `open_window` 方法。
        - `open_window` 檢查對應類型的窗口是否已打開且可見。
        - 如果可見，則關閉該窗口。
        - 如果不可見或不存在，則創建新窗口實例 (如 `ShipmentWindow`, `SettingsWindow` 等)，設置其位置在主窗口右側，並顯示。
        - 使用 `open_windows` 字典追蹤已打開的窗口。
    - **登出：** 點擊登出按鈕觸發 `logout` 方法。
        - 關閉所有已打開的功能窗口。
        - 關閉主窗口。
    - **尺寸調整：** `apply_size_setting` 方法根據選擇的尺寸 (`default`, `small`, `medium`, `large`) 調整主窗口寬度、導航欄佈局、導航按鈕大小/圖標/字體、底部佈局、登出按鈕大小等。
    - **關閉窗口：** `closeEvent` 觸發 `save_window_position` 保存當前窗口位置到 `settings.json`，並關閉所有子窗口。
- **涉及數據：**
    - `settings.json` (保存窗口尺寸 'size', 窗口位置 'window_pos_x', 'window_pos_y')

### 3.3 出貨鏡頭窗口 (`content_windows.py` - `ShipmentWindow`)

- **界面元素：** (基於 `BaseContentWindow`)
    - 標題欄 (含釘選、標題、關閉按鈕)
    - 狀態顯示區 (錄製狀態圖標、文字)
    - 按鈕區 (停止記錄、強制存檔)
    - 攝像頭畫面顯示區 (多個，例如 4 個 QFrame)
    - 記錄列表區 (QScrollArea > QWidget > QVBoxLayout)
        - 每個記錄項 (QFrame) 包含：時間戳、狀態圖標、操作按鈕 (播放、刪除)
- **交互邏輯：**
    - (待實現) 啟動/停止錄製。
    - (待實現) 顯示攝像頭畫面。
    - (待實現) 保存/讀取出貨記錄。
    - (待實現) 播放/刪除單條記錄。
- **涉及數據：**
    - (未來) `shipment_records` 表 (記錄ID, 時間戳, 狀態, 視頻文件路徑, 關聯訂單號等)
    - (未來) `camera_settings` 表 (攝像頭ID, 名稱, URL/索引, 分組)

### 3.4 進行出貨窗口 (`content_windows.py` - `ProcessShipmentWindow`)

- **界面元素：** (基於 `BaseContentWindow`)
    - 標題欄
    - 產品掃描輸入框 (QLineEdit)
    - 掃描按鈕 (QPushButton)
    - 包裹掃描輸入框 (QLineEdit)
    - 掃描按鈕 (QPushButton)
    - 信息顯示區 (QLabel - 顯示掃描結果、錯誤信息等)
    - 手動輸入按鈕 (QPushButton)
    - 強制出貨按鈕 (QPushButton)
- **交互邏輯：**
    - (待實現) 掃描產品條碼/QR碼。
    - (待實現) 掃描包裹條碼/QR碼。
    - (待實現) 驗證產品與包裹的匹配關係。
    - (待實現) 記錄出貨信息。
    - (待實現) 提供手動輸入出貨信息的彈窗或界面。
    - (待實現) 強制完成出貨流程。
- **涉及數據：**
    - (未來) `products` 表 (產品ID, 條碼, 名稱, 規格)
    - (未來) `packages` 表 (包裹ID, 條碼, 狀態)
    - (未來) `shipments` 表 (出貨ID, 產品ID, 包裹ID, 操作員ID, 時間戳, 狀態)

### 3.5 服務訂閱窗口 (`content_windows.py` - `SubscriptionWindow`)

- **界面元素：** (基於 `BaseContentWindow`)
    - 標題欄
    - 訂閱狀態顯示 (QLabel)
    - 到期日顯示 (QLabel)
    - 續訂/購買按鈕 (QPushButton)
    - (可能) 訂閱方案介紹
- **交互邏輯：**
    - (待實現) 從後端 API 獲取當前訂閱狀態和到期日並顯示。
    - (待實現) 點擊續訂/購買按鈕，跳轉到支付頁面或調用支付接口。
- **涉及數據：**
    - (未來) `subscriptions` 表 (用戶ID, 訂閱狀態, 方案類型, 到期日)
    - (未來) 後端 API (獲取訂閱信息, 處理支付)

### 3.6 系統設定窗口 (`settings_window.py`)

- **界面元素：** (基於 `BaseContentWindow`)
    - 標題欄
    - **顯示設置區塊：**
        - 標題 (QLabel)
        - 窗口與圖標大小標籤 (QLabel)
        - 尺寸按鈕組 (QPushButton - 預設, 小, 中, 大)
        - 比例說明 (QLabel)
        - 注意事項 (QLabel)
- **交互邏輯：**
    - 點擊尺寸按鈕 (`change_size`):
        - 更新按鈕的選中狀態。
        - 獲取主窗口實例。
        - 調用主窗口的 `apply_size_setting` 方法，傳遞選擇的尺寸標識符 ('default', 'small', 'medium', 'large')。
    - `get_current_size` 方法讀取主窗口當前的 `size_setting`，用於初始化按鈕選中狀態。
- **涉及數據：**
    - `settings.json` (通過主窗口間接讀寫 'size') 