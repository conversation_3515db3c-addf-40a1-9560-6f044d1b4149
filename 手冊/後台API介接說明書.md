# 後台 API 介接說明書

## 概述

本文檔定義了「出貨記錄系統」桌面應用程式與後端服務器進行交互的 API 接口規範。

**注意：** 目前應用程式尚未實現與後端 API 的交互，以下接口為預留設計。

**基礎 URL:** (待定, 例如: `https://api.yourdomain.com/shipment/v1`)

**認證方式:** (待定, 例如: Bearer Token in Authorization header)

## API 接口

### 1. 用戶認證

#### 1.1 登入驗證與訂閱狀態檢查

- **Endpoint:** `POST /auth/login`
- **描述:** 驗證用戶憑證，檢查訂閱狀態和唯一登入限制。
- **請求 Body (JSON):**
  ```json
  {
    "username": "string",
    "password": "string",
    "mac_address": "string",
    "system_info": "string"
  }
  ```
- **響應 (成功 - 200 OK):**
  ```json
  {
    "access_token": "string", // 用於後續請求的認證 token
    "user_id": "integer",
    "username": "string",
    "subscription_status": "string", // e.g., "active", "expired"
    "expiry_date": "YYYY-MM-DD",
    "message": "Login successful"
  }
  ```
- **響應 (失敗 - 401 Unauthorized):**
  ```json
  {
    "error": "Invalid credentials" 
  }
  ```
- **響應 (失敗 - 403 Forbidden):**
  ```json
  {
    "error": "Subscription expired" 
    // 或 "Device limit reached"
  }
  ```

#### 1.2 登出

- **Endpoint:** `POST /auth/logout`
- **描述:** 通知後端用戶登出 (例如，解除設備鎖定)。
- **請求 Headers:**
  `Authorization: Bearer <access_token>`
- **響應 (成功 - 200 OK):**
  ```json
  {
    "message": "Logout successful"
  }
  ```

### 2. 訂閱管理

#### 2.1 獲取當前訂閱信息

- **Endpoint:** `GET /subscriptions/current`
- **描述:** 獲取當前登入用戶的詳細訂閱信息。
- **請求 Headers:**
  `Authorization: Bearer <access_token>`
- **響應 (成功 - 200 OK):**
  ```json
  {
    "user_id": "integer",
    "plan_type": "string",
    "status": "string",
    "start_date": "YYYY-MM-DD",
    "expiry_date": "YYYY-MM-DD",
    "auto_renew": "boolean"
  }
  ```

#### 2.2 (待定) 獲取續訂/購買鏈接

- **Endpoint:** `GET /subscriptions/purchase_url`
- **描述:** 獲取用於續訂或購買的支付頁面 URL。
- **請求 Headers:**
  `Authorization: Bearer <access_token>`
- **響應 (成功 - 200 OK):**
  ```json
  {
    "purchase_url": "string"
  }
  ```

### 3. 出貨記錄 (如果需要雲端同步)

#### 3.1 上傳出貨記錄

- **Endpoint:** `POST /records`
- **描述:** 上傳本地的出貨記錄 (包含視頻文件信息)。
- **請求 Headers:**
  `Authorization: Bearer <access_token>`
- **請求 Body (JSON):**
  ```json
  {
    "timestamp": "YYYY-MM-DDTHH:mm:ssZ",
    "status": "string", // "completed", "error"
    "video_file_name": "string", // 文件名，可能需要單獨的文件上傳接口
    "duration_seconds": "integer",
    "camera_group": "string",
    "notes": "string",
    "shipment_details": { // 關聯的出貨信息 (可選)
      "product_barcode": "string",
      "package_barcode": "string"
    }
  }
  ```
- **響應 (成功 - 201 Created):**
  ```json
  {
    "record_id": "integer", // 後端生成的記錄 ID
    "message": "Record uploaded successfully"
  }
  ```

#### 3.2 (待定) 文件上傳接口

- **Endpoint:** (待定, 例如 `POST /records/upload_video`)
- **描述:** 專門用於上傳視頻文件，可能使用 multipart/form-data。

#### 3.3 (待定) 獲取出貨記錄列表

- **Endpoint:** `GET /records`
- **描述:** 獲取用戶的出貨記錄列表 (分頁)。
- **請求 Headers:**
  `Authorization: Bearer <access_token>`
- **請求 Query Parameters:**
  `page=integer`
  `limit=integer`
  `start_date=YYYY-MM-DD`
  `end_date=YYYY-MM-DD`
- **響應 (成功 - 200 OK):**
  ```json
  {
    "records": [ /* 記錄對象列表 */ ],
    "pagination": { /* 分頁信息 */ }
  }
  ``` 