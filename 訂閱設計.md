# 訂閱系統 API 規劃

本文檔旨在規劃用於「出貨輔助 Pro」應用程式的訂閱系統後端 API。

## 1. 概述

此 API 旨在讓桌面應用程式能夠：

1.  驗證用戶（基於用戶名/MAC 地址或其他唯一標識）的訂閱狀態。
2.  獲取可用的訂閱方案。
3.  （未來可能）啟動訂閱/續訂流程。

API 將採用 RESTful 風格，使用 JSON 格式進行數據交換。

## 2. 認證機制

每次 API 請求都需要進行認證，以確保請求來自合法的用戶和應用程式實例。

**方案建議：**

*   **基於令牌 (Token-Based):**
    *   桌面應用程式在首次登入或驗證成功後，從伺服器獲取一個有時效性的 API Token。
    *   後續請求在 HTTP Header (`Authorization: Bearer <token>`) 中攜帶此 Token。
    *   伺服器驗證 Token 的有效性。
*   **基於應用程式憑證 + 用戶標識:**
    *   桌面應用程式持有一個固定的 API Key 和 Secret (用於標識應用程式本身)。
    *   請求體或 Header 中包含用戶的唯一標識（如用戶名）和設備標識（如 MAC 地址）。
    *   伺服器驗證 API Key/Secret，並結合用戶名和 MAC 地址查詢訂閱狀態。

**初步選用：** 考慮到桌面應用可能需要離線驗證（或緩存狀態），**基於應用程式憑證 + 用戶標識** 可能更易於初步實現，但安全性相對較低。長遠來看，應考慮**基於令牌**的方式。使用多重設備標識（如 MAC 地址和主機板序號）可以提高設備識別的準確性。規劃中將假設使用某種形式的認證（具體細節待定），並在請求說明中註明。

## 3. API 端點 (Endpoints)

### 3.1. 檢查訂閱狀態

*   **目的:** 驗證指定用戶和設備的當前訂閱狀態、有效期及權限。這是應用程式啟動或關鍵操作前必須調用的核心接口。
*   **Method:** `POST`
*   **Endpoint:** `/api/v1/subscription/check`
*   **認證:** 需要（具體方式待定，可能包含 API Key/Secret 或 Token）
*   **Request Body (JSON):**
    ```json
    {
      "username": "<EMAIL>", 
      "mac_address": "00:1A:2B:3C:4D:5E",
      "board_serial": "ABC123XYZ",
      "system_info": "Windows-10-... (optional)" 
    }
    ```
*   **Success Response (200 OK):**
    ```json
    {
      "status": "active", // "active", "inactive", "expired", "trial"
      "plan_id": "pro_monthly", 
      "plan_name": "專業版 - 月付",
      "expiry_date": "2024-12-31T23:59:59Z", // ISO 8601 格式, null 如果是永久或無效
      "permissions": ["feature_a", "feature_b"], // 允許使用的功能標識列表
      "message": "訂閱有效" 
    }
    ```
    或 (未訂閱/過期):
    ```json
    {
      "status": "inactive", // or "expired"
      "plan_id": null,
      "plan_name": null,
      "expiry_date": null,
      "permissions": [],
      "message": "用戶未訂閱或訂閱已過期"
    }
    ```
*   **Error Responses:**
    *   `400 Bad Request`: 請求體格式錯誤或缺少必要參數。
    *   `401 Unauthorized`: 認證失敗（API Key 無效、Token 過期等）。
    *   `404 Not Found`: 用戶不存在（較少見，取決於業務邏輯）。
    *   `500 Internal Server Error`: 伺服器內部錯誤。

### 3.2. 獲取訂閱方案列表 (可選)

*   **目的:** 獲取當前可供用戶選擇或查看的訂閱方案詳情。
*   **Method:** `GET`
*   **Endpoint:** `/api/v1/plans`
*   **認證:** 可能需要（取決於是否需要根據用戶顯示不同方案）
*   **Success Response (200 OK):**
    ```json
    {
      "plans": [
        {
          "plan_id": "pro_monthly",
          "name": "專業版 - 月付",
          "description": "按月訂閱，解鎖所有專業功能。",
          "price": 10.00,
          "currency": "USD",
          "billing_cycle": "monthly", // "monthly", "yearly"
          "features": ["功能 A", "功能 B", "優先支持"]
        },
        {
          "plan_id": "pro_yearly",
          "name": "專業版 - 年付",
          "description": "按年訂閱，享折扣價格。",
          "price": 100.00,
          "currency": "USD",
          "billing_cycle": "yearly",
          "features": ["功能 A", "功能 B", "優先支持"]
        }
      ]
    }
    ```
*   **Error Responses:**
    *   `401 Unauthorized`: 如果需要認證但失敗。
    *   `500 Internal Server Error`: 伺服器內部錯誤。

### 3.3. 啟動訂閱/支付流程 (未來規劃)

*   **目的:** 將用戶引導至支付網關或應用內購買流程以完成訂閱。
*   **Method:** `POST`
*   **Endpoint:** `/api/v1/subscription/initiate`
*   **認證:** 需要
*   **Request Body (JSON):**
    ```json
    {
      "username": "<EMAIL>",
      "mac_address": "00:1A:2B:3C:4D:5E",
      "board_serial": "ABC123XYZ",
      "plan_id": "pro_monthly"
    }
    ```
*   **Success Response (200 OK):**
    ```json
    {
      "redirect_url": "https://payment.gateway.com/...", // 跳轉到支付頁面
      "transaction_id": "...", // 內部交易 ID
      "message": "請跳轉至支付頁面完成訂閱。"
    }
    ```
    *   **注意:** 實際流程可能更複雜，例如需要處理支付回調 (Webhook)。

## 4. 數據模型 (概念)

後端需要維護至少以下數據模型：

*   **用戶 (User):** `user_id`, `username`, `email`, `password_hash` (使用 **Argon2id** 算法生成), `creation_date`, ...
*   **設備 (Device):** `device_id`, `user_id` (外鍵), `mac_address`, `board_serial` (主機板序號), `system_info`, `last_seen`, ... (可選，用於設備管理)
*   **訂閱方案 (Plan):** `plan_id`, `name`, `description`, `price`, `currency`, `billing_cycle`, `features`, ...
*   **用戶訂閱 (Subscription):** `subscription_id`, `user_id` (外鍵), `plan_id` (外鍵), `status` (`active`, `inactive`, `expired`, `trial`), `start_date`, `expiry_date`, `auto_renew`, `last_payment_date`, `payment_gateway_ref`, ...

## 5. 後續步驟

1.  確定最終的認證方案。
2.  詳細設計數據庫模型。
3.  選擇後端技術棧並實現 API 端點。
4.  編寫 API 文檔 (例如使用 Swagger/OpenAPI)。
5.  進行安全審計。
6.  在桌面應用程式中集成 API 調用。 