"""
'系統設定' 功能頁面模塊。

包含 SettingsWindow 類，繼承自 BaseContentWindow。
負責顯示和處理應用程式的各種設置選項，例如：
- 界面尺寸調整。
- (未來可能包含) 主題選擇、數據庫連接、API 密鑰等。
允許用戶修改設置並將更改應用到主窗口或保存到配置中。
"""
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFrame, QSizePolicy, QApplication, QTabWidget, QComboBox
)
from PySide6.QtCore import Qt
from PySide6.QtCore import Signal
from PySide6.QtGui import QFont, QIcon
from PySide6.QtWidgets import QMainWindow

from base_window import BaseContentWindow
from config_manager import ConfigManager

class SettingsWindow(BaseContentWindow):
    size_setting_changed = Signal(str)
    
    def __init__(self, window_type):
        self.config_manager = ConfigManager()
        super().__init__("系統設定", window_type)
        self.size_ratios = {
            "default": 1.0,
            "small": 0.85,
            "medium": 1.2,
            "large": 1.4
        }
        
    def init_ui(self):
        super().init_ui()
        
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet(""" /* Tab styles */
            QTabWidget::pane { border-top: 2px solid #C2C7CB; position: absolute; top: -0.5em; }
            QTabWidget::tab-bar { alignment: left; }
            QTabBar::tab { background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #E1E1E1, stop: 1.0 #D3D3D3); border: 1px solid #C4C4C3; border-bottom-color: #C2C7CB; border-top-left-radius: 4px; border-top-right-radius: 4px; min-width: 8ex; padding: 5px 10px; margin-right: 2px; }
            QTabBar::tab:selected, QTabBar::tab:hover { background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #fafafa, stop: 1.0 #fafafa); }
            QTabBar::tab:selected { border-color: #9B9B9B; border-bottom-color: #C2C7CB; }
        """)

        appearance_tab = QWidget()
        appearance_layout = QVBoxLayout(appearance_tab)
        appearance_layout.setContentsMargins(10, 10, 10, 10)
        appearance_layout.setSpacing(15)
        
        display_group = QFrame()
        display_group.setStyleSheet("QFrame { background-color: white; border-radius: 5px; }")
        display_layout = QVBoxLayout(display_group)
        
        display_title = QLabel("顯示設置")
        display_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #333333; padding: 5px 0;")
        display_layout.addWidget(display_title)
        
        # 窗口與圖標大小設置
        size_layout = QHBoxLayout()
        size_label = QLabel("窗口與圖標大小:")
        size_label.setStyleSheet("font-size: 16px; color: #444444;")
        self.default_btn = QPushButton("預設 (85%)")
        self.small_btn = QPushButton("小 (70%)")
        self.medium_btn = QPushButton("中 (120%)")
        self.large_btn = QPushButton("大 (140%)")
        size_btn_style = """ /* Button styles */
            QPushButton { background-color: #f0f0f0; border: 1px solid #cccccc; border-radius: 4px; padding: 8px 16px; font-size: 14px; min-width: 80px; }
            QPushButton:hover { background-color: #e0e0e0; }
            QPushButton:checked { background-color: #4378de; color: white; border: 1px solid #3268ce; }
        """
        self.default_btn.setStyleSheet(size_btn_style)
        self.small_btn.setStyleSheet(size_btn_style)
        self.medium_btn.setStyleSheet(size_btn_style)
        self.large_btn.setStyleSheet(size_btn_style)
        self.default_btn.setCheckable(True)
        self.small_btn.setCheckable(True)
        self.medium_btn.setCheckable(True)
        self.large_btn.setCheckable(True)
        
        current_size = self.config_manager.load_size_setting()
        self.default_btn.setChecked(current_size == "default")
        self.small_btn.setChecked(current_size == "small")
        self.medium_btn.setChecked(current_size == "medium")
        self.large_btn.setChecked(current_size == "large")
        self.default_btn.clicked.connect(lambda: self.change_size("default"))
        self.small_btn.clicked.connect(lambda: self.change_size("small"))
        self.medium_btn.clicked.connect(lambda: self.change_size("medium"))
        self.large_btn.clicked.connect(lambda: self.change_size("large"))
        size_btn_layout = QHBoxLayout()
        size_btn_layout.addWidget(self.default_btn)
        size_btn_layout.addWidget(self.small_btn)
        size_btn_layout.addWidget(self.medium_btn)
        size_btn_layout.addWidget(self.large_btn)
        size_btn_layout.addStretch()
        size_layout.addWidget(size_label)
        size_layout.addStretch()
        display_layout.addLayout(size_layout)
        display_layout.addLayout(size_btn_layout)

        ratio_frame = QFrame()
        ratio_frame.setStyleSheet("background-color: #f9f9f9; border-radius: 4px; padding: 5px;")
        ratio_layout = QVBoxLayout(ratio_frame)
        ratio_title = QLabel("尺寸比例說明")
        ratio_title.setStyleSheet("font-size: 14px; font-weight: bold; color: #444444;")
        ratio_desc = QLabel(
            "預設：界面元素縮小至85%\n"
            "小：界面元素縮小至70%\n"
            "中：界面元素放大至120%\n"
            "大：界面元素放大至140%"
        )
        ratio_desc.setStyleSheet("font-size: 13px; color: #555555;")
        ratio_layout.addWidget(ratio_title)
        ratio_layout.addWidget(ratio_desc)
        display_layout.addWidget(ratio_frame)
        
        note_label = QLabel("注意: 更改設置後將立即應用。")
        note_label.setStyleSheet("color: #666666; font-style: italic; margin-top: 5px;")
        display_layout.addWidget(note_label)
        
        appearance_layout.addWidget(display_group)
        appearance_layout.addStretch()
        
        general_tab = QWidget()
        general_layout = QVBoxLayout(general_tab)
        general_label = QLabel("此處為一般設定 (待開發)")
        general_label.setAlignment(Qt.AlignCenter)
        general_layout.addWidget(general_label)
        
        about_tab = QWidget()
        about_layout = QVBoxLayout(about_tab)
        about_label = QLabel("版本資訊等 (待開發)")
        about_label.setAlignment(Qt.AlignCenter)
        about_layout.addWidget(about_label)

        tab_widget.addTab(appearance_tab, "外觀")
        tab_widget.addTab(general_tab, "一般")
        tab_widget.addTab(about_tab, "關於")

        self.content_layout.addWidget(tab_widget)
        
    def change_size(self, size):
        """更改窗口和圖標尺寸"""
        # 更新按鈕選中狀態
        self.default_btn.setChecked(size == "default")
        self.small_btn.setChecked(size == "small")
        self.medium_btn.setChecked(size == "medium")
        self.large_btn.setChecked(size == "large")
        
        # 調用 ConfigManager 的方法來保存設置並發射信號
        self.config_manager.save_size_setting(size)
        self.size_setting_changed.emit(size)
        print(f"尺寸設置已更改為: {size} (信號已發射)")

    # --- 移除保存出貨窗口尺寸的方法 --- 
    # def save_shipment_window_size_text(self, selected_text):
    #     """保存選擇的出貨窗口尺寸文本到設置"""
    #     # 移除 self.shipment_size_options 的引用
    #     # if selected_text in self.shipment_size_options:
    #     if selected_text in ["小 (280px)", "預設 (320px)", "中 (420px)", "大 (480px)"]: # 直接判斷文本
    #         print(f"準備保存出貨窗口尺寸設置文本: {selected_text}")
    #         # 直接調用 main_window 的保存方法
    #         settings = self.main_window.load_settings()
    #         settings['shipment_window_size_text'] = selected_text
    #         if self.main_window.save_settings(settings):
    #             print(f"成功保存出貨窗口尺寸設置文本: {selected_text}")
    #         else:
    #             print(f"警告：保存出貨窗口尺寸設置文本失敗")
    #     else:
    #          print(f"警告：無效的出貨窗口尺寸選項 '{selected_text}'") 
    # --- 結束移除 --- 