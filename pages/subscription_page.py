"""
'服務訂閱' 功能頁面模塊。

包含 SubscriptionWindow 類，繼承自 BaseContentWindow。
負責顯示用戶的訂閱狀態、到期日期等信息，
並可能提供續訂或更改訂閱計劃的選項。
"""
# --- 新增導入 ---
import json
import requests # 需要 pip install requests
from datetime import datetime, timezone
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QFrame,
    QHBoxLayout, QSizePolicy, QMessageBox
)
from PySide6.QtCore import Qt
from base_window import BaseContentWindow
# --- 結束新增 ---

class SubscriptionWindow(BaseContentWindow):
    # --- 修改 __init__ 以接收 user_data ---
    def __init__(self, window_type, user_data=None):
        self.user_data = user_data or {} # 存儲用戶數據
        # !! 請替換成您的實際 API URL !!
        self.api_url = "YOUR_BACKEND_API_URL/api/v1/subscription/check"
        super().__init__("服務訂閱", window_type)
        self.load_subscription_data() # 初始化後加載數據
    # --- 結束修改 ---

    # --- 重寫 init_ui --- 
    def init_ui(self):
        super().init_ui() # 調用父類的 init_ui 來創建基礎框架

        # 主內容佈局
        content_v_layout = QVBoxLayout()
        content_v_layout.setContentsMargins(20, 15, 20, 20)
        content_v_layout.setSpacing(15)

        # 創建顯示信息的標籤
        self.status_label_layout = self._create_info_label("訂閱狀態:", "讀取中...")
        self.plan_label_layout = self._create_info_label("訂閱方案:", "讀取中...")
        self.expiry_label_layout = self._create_info_label("到期日期:", "讀取中...")
        self.remaining_label_layout = self._create_info_label("剩餘天數:", "讀取中...")

        # 將標籤佈局添加到主內容佈局
        content_v_layout.addLayout(self.status_label_layout)
        content_v_layout.addLayout(self.plan_label_layout)
        content_v_layout.addLayout(self.expiry_label_layout)
        content_v_layout.addLayout(self.remaining_label_layout)

        content_v_layout.addStretch(1) # 將內容推到頂部

        # 將新的內容佈局設置到父類提供的 content_frame 中
        # 清空 content_frame 可能存在的舊佈局 (如果父類 init_ui 添加了東西)
        old_layout = self.content_frame.layout()
        if old_layout is not None:
            while old_layout.count():
                item = old_layout.takeAt(0)
                widget = item.widget()
                if widget is not None:
                    widget.setParent(None)
                else:
                    layout_item = item.layout()
                    if layout_item is not None:
                        # 遞歸清空子佈局
                        while layout_item.count():
                            sub_item = layout_item.takeAt(0)
                            sub_widget = sub_item.widget()
                            if sub_widget is not None:
                                sub_widget.setParent(None)
            # 刪除舊佈局本身
            QWidget().setLayout(old_layout)

        self.content_frame.setLayout(content_v_layout)
        self.setMinimumWidth(350) # 設置一個最小寬度
        self.adjustSize() # 調整窗口大小以適應內容

    def _create_info_label(self, title, initial_value=""):
        """輔助函數：創建一個標題 + 值的水平佈局"""
        layout = QHBoxLayout()
        title_label = QLabel(title)
        title_label.setStyleSheet("font-weight: bold; color: #333;")
        title_label.setFixedWidth(80) # 固定標題寬度以便對齊

        value_label = QLabel(initial_value)
        value_label.setStyleSheet("color: #555;")
        value_label.setWordWrap(True) # 允許值換行

        layout.addWidget(title_label)
        layout.addWidget(value_label, 1) # 值標籤佔用更多空間
        layout.addStretch(0)

        # 存儲值標籤的引用，以便後續更新
        label_attribute_name = f"{title.split(':')[0]}_value_label"
        setattr(self, label_attribute_name, value_label)
        return layout

    def load_subscription_data(self):
        """從後端 API 加載訂閱數據並更新 UI"""
        required_keys = ['username', 'mac_address', 'board_serial']
        if not self.user_data or not all(key in self.user_data for key in required_keys):
            self._update_ui_error("缺少必要的用戶信息無法查詢。")
            return

        payload = {
            "username": self.user_data.get("username"),
            "mac_address": self.user_data.get("mac_address"),
            "board_serial": self.user_data.get("board_serial"),
        }
        headers = {
            "Content-Type": "application/json",
            # "Authorization": "Bearer YOUR_TOKEN" # <--- 如果需要 Token 認證
            # "X-Api-Key": "YOUR_APP_KEY"       # <--- 如果需要 API Key 認證
        }

        try:
            response = requests.post(self.api_url, headers=headers, json=payload, timeout=10)
            response.raise_for_status() # 檢查 HTTP 錯誤 (4xx, 5xx)

            data = response.json()
            self._update_ui(data)

        except requests.exceptions.Timeout:
            self._update_ui_error("請求超時，請檢查網絡連接。")
        except requests.exceptions.ConnectionError:
            self._update_ui_error("無法連接到伺服器，請檢查網絡或 API URL。")
        except requests.exceptions.HTTPError as e:
            error_message = f"API 請求失敗 (HTTP {e.response.status_code})。"
            try:
                # 嘗試解析錯誤響應體
                error_data = e.response.json()
                error_message += f" 錯誤信息: {error_data.get('message', e.response.text)}"
            except (json.JSONDecodeError, ValueError):
                # 使用 repr() 避免潛在的非字符串錯誤內容
                error_content = repr(e.response.content)
                error_message += f" 錯誤內容: {error_content[:100]}..."
            self._update_ui_error(error_message)
        except requests.exceptions.RequestException as e:
            self._update_ui_error(f"請求發生未知錯誤: {e}")
        except Exception as e:
            # 捕獲更廣泛的異常，例如 JSON 解析成功但內容意外
            self._update_ui_error(f"處理響應時發生錯誤: {e}")

    def _update_ui(self, data):
        """使用從 API 獲取的數據更新 UI 標籤"""
        status_map = {
            "active": "有效",
            "inactive": "無效",
            "expired": "已過期",
            "trial": "試用中"
        }
        status_text = status_map.get(data.get("status", "unknown"), "未知狀態")
        plan_name = data.get("plan_name", "N/A")
        expiry_date_str = data.get("expiry_date")

        expiry_text = "N/A"
        remaining_days_text = "N/A"

        if expiry_date_str:
            try:
                # 解析 ISO 8601 格式日期時間字符串
                expiry_dt = datetime.fromisoformat(expiry_date_str.replace('Z', '+00:00'))
                expiry_dt_local = expiry_dt.astimezone()
                expiry_text = expiry_dt_local.strftime("%Y-%m-%d %H:%M:%S")

                # 計算剩餘天數
                now_utc = datetime.now(timezone.utc)
                remaining_delta = expiry_dt - now_utc
                remaining_days = remaining_delta.days

                if status_text == "有效" or status_text == "試用中": # 只在有效或試用時顯示剩餘天數
                    if remaining_days >= 0:
                        remaining_days_text = f"{remaining_days} 天"
                    else:
                        remaining_days_text = "已過期" # 理論上不應發生，但作為防禦
                        status_text = "已過期" # 同步狀態顯示
                elif status_text == "已過期":
                    remaining_days_text = "已過期"
                else:
                    remaining_days_text = "-" # 無效或未知狀態不顯示天數

            except ValueError:
                expiry_text = "日期格式錯誤"
                remaining_days_text = "無法計算"
            except Exception as e: # 捕獲時區轉換等其他日期錯誤
                expiry_text = "日期處理錯誤"
                remaining_days_text = "無法計算"
                print(f"處理日期時出錯: {e}")

        # 更新 QLabel 的文本
        if hasattr(self, '訂閱狀態_value_label'): self.訂閱狀態_value_label.setText(status_text)
        if hasattr(self, '訂閱方案_value_label'): self.訂閱方案_value_label.setText(plan_name)
        if hasattr(self, '到期日期_value_label'): self.到期日期_value_label.setText(expiry_text)
        if hasattr(self, '剩餘天數_value_label'): self.剩餘天數_value_label.setText(remaining_days_text)

        self.adjustSize() # 更新後重新調整窗口大小

    def _update_ui_error(self, message):
        """在 UI 上顯示錯誤信息"""
        error_text = f"錯誤: {message}"
        print(error_text) # 同時打印到控制台
        if hasattr(self, '訂閱狀態_value_label'): self.訂閱狀態_value_label.setText("錯誤")
        if hasattr(self, '訂閱方案_value_label'): self.訂閱方案_value_label.setText("-")
        if hasattr(self, '到期日期_value_label'): self.到期日期_value_label.setText("-")
        if hasattr(self, '剩餘天數_value_label'): self.剩餘天數_value_label.setText("-")
        # 可以選擇彈出消息框提示用戶
        # QMessageBox.warning(self, "讀取錯誤", message)
        self.adjustSize() 