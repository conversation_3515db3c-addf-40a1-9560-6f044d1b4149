"""
'進行出貨' 功能頁面模塊。

包含 ProcessShipmentWindow 類，繼承自 BaseContentWindow。
負責顯示與出貨流程相關的界面元素，例如：
- 狀態指示燈和標籤。
- 停止紀錄和強制儲存按鈕。
- 條碼檢測攝像頭畫面 (模擬)。
- 出貨紀錄攝像頭畫面 (模擬)。
"""
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFrame, QSizePolicy
)
from PySide6.QtCore import Qt
from base_window import BaseContentWindow

class ProcessShipmentWindow(BaseContentWindow):
    def __init__(self, window_type):
        super().__init__("進行出貨", window_type)
        
    def init_ui(self):
        super().init_ui()
        
        main_content_layout = QVBoxLayout()
        main_content_layout.setSpacing(5)
        
        status_layout = QHBoxLayout()
        
        status_container = QHBoxLayout()
        status_container.setSpacing(5)
        
        status_icon = QLabel()
        status_icon.setFixedSize(16, 16)
        status_icon.setStyleSheet("background-color: #dc3545; border-radius: 8px;")
        
        status_label = QLabel("出貨紀錄中")
        status_label.setObjectName("recording")
        status_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #333333; background-color: transparent;")
        
        status_container.addWidget(status_icon)
        status_container.addWidget(status_label)
        status_container.addStretch()
        
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        stop_record_button = QPushButton("停止紀錄")
        stop_record_button.setObjectName("record_button")
        
        force_save_button = QPushButton("強制儲存")
        force_save_button.setObjectName("force_save")
        
        buttons_layout.addWidget(stop_record_button)
        buttons_layout.addWidget(force_save_button)
        
        status_layout.addLayout(status_container)
        status_layout.addStretch()
        status_layout.addLayout(buttons_layout)
        
        status_bar_container = QWidget()
        status_bar_container.setLayout(status_layout)
        status_bar_container.setFixedWidth(320)
        
        cameras_layout = QVBoxLayout()
        cameras_layout.setSpacing(5)
        
        barcode_camera = QFrame()
        barcode_camera.setStyleSheet("background-color: #333333; border-radius: 5px;")
        barcode_camera.setFixedSize(320, 180)
        barcode_layout = QVBoxLayout(barcode_camera)
        
        reading_label = QLabel("讀取中...")
        reading_label.setAlignment(Qt.AlignCenter)
        reading_label.setStyleSheet("color: white; font-size: 16px;")
        
        date_label = QLabel("2025/5/15 15:22:33")
        date_label.setAlignment(Qt.AlignLeft | Qt.AlignBottom)
        date_label.setStyleSheet("color: white; font-size: 14px;")
        
        camera_label_1 = QLabel("條碼檢測攝像頭")
        camera_label_1.setObjectName("camera_label")
        camera_label_1.setAlignment(Qt.AlignCenter)
        
        barcode_layout.addStretch()
        barcode_layout.addWidget(reading_label)
        barcode_layout.addStretch()
        barcode_layout.addWidget(date_label)
        barcode_layout.addWidget(camera_label_1, 0, Qt.AlignCenter)
        
        shipment_camera = QFrame()
        shipment_camera.setStyleSheet("background-color: #333333; border-radius: 5px;")
        shipment_camera.setFixedSize(320, 180)
        shipment_layout = QVBoxLayout(shipment_camera)
        
        order_label = QLabel("訂單編號: 13612626162")
        order_label.setAlignment(Qt.AlignRight | Qt.AlignTop)
        order_label.setStyleSheet("color: white; font-size: 14px;")
        
        date_label_2 = QLabel("2025/5/15 15:22:33")
        date_label_2.setAlignment(Qt.AlignLeft | Qt.AlignBottom)
        date_label_2.setStyleSheet("color: white; font-size: 14px;")
        
        camera_label_2 = QLabel("出貨紀錄攝像頭")
        camera_label_2.setObjectName("camera_label")
        camera_label_2.setAlignment(Qt.AlignCenter)
        
        shipment_layout.addWidget(order_label)
        shipment_layout.addStretch()
        shipment_layout.addWidget(date_label_2)
        shipment_layout.addWidget(camera_label_2, 0, Qt.AlignCenter)
        
        cameras_layout.addWidget(barcode_camera)
        cameras_layout.addWidget(shipment_camera)
        
        main_content_layout.addWidget(status_bar_container, 0, Qt.AlignLeft)
        main_content_layout.addLayout(cameras_layout)

        self.content_layout.addLayout(main_content_layout)
        self.content_layout.addStretch()

        self.setSizePolicy(QSizePolicy.Maximum, QSizePolicy.Preferred)
        self.adjustSize() 