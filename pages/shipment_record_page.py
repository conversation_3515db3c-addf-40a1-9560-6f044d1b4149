"""
'出貨記錄' 功能頁面模塊。

包含 ShipmentRecordWindow 類，繼承自 BaseContentWindow。
負責顯示歷史出貨記錄的列表或表格。
(目前實現較為簡單，僅作為佔位符)
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                               QFrame, QLineEdit, QTreeView, QSplitter, QFormLayout,
                               QDateEdit, QCheckBox)
from PySide6.QtCore import Qt, QModelIndex
from PySide6.QtCore import QDate
from PySide6.QtGui import QFont, QStandardItemModel, QStandardItem
from base_window import BaseContentWindow

class ShipmentRecordWindow(BaseContentWindow):
    def __init__(self, window_type):
        super().__init__("出貨記錄", window_type)
        self.resize(800, 500) # 給窗口一個初始大小
        
    def init_ui(self):
        super().init_ui()
        # 不調用 super().init_ui() 因為我們要完全替換 content_layout
        # 但我們需要父類的基礎框架，所以在 __init__ 中調用 super().__init__
        # self.content_frame 和 self.content_layout 由父類創建，但我們會替換 self.content_frame 的佈局
        
        # 1. 創建主分割佈局 (左右分割)
        main_splitter = QSplitter(Qt.Horizontal)

        # 2. 創建左側部件和佈局
        left_widget = QFrame()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(5)

        # 2.1 搜索欄區域
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("按條碼或內容搜索...")
        search_layout.addWidget(self.search_input) # 輸入框佔滿空間
        left_layout.addLayout(search_layout)

        # 2.2 日期範圍篩選區域
        date_filter_layout = QHBoxLayout()
        date_filter_layout.setSpacing(5)
        self.date_filter_checkbox = QCheckBox("啟用日期篩選:")
        self.date_filter_checkbox.setChecked(False) # 默認不啟用
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate().addDays(-60)) # 默認 60 天前
        end_date_label = QLabel("-") # 用一個標籤分隔
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate.currentDate()) # 默認今天
        
        date_filter_layout.addWidget(self.date_filter_checkbox)
        date_filter_layout.addWidget(self.start_date_edit)
        date_filter_layout.addWidget(end_date_label) # 添加分隔符
        date_filter_layout.addWidget(self.end_date_edit)
        date_filter_layout.addStretch() # 將日期選擇器推到左側
        left_layout.addLayout(date_filter_layout)

        # 2.3 樹狀視圖區域
        self.record_tree = QTreeView()
        self.record_tree.setHeaderHidden(True) # 隱藏表頭
        left_layout.addWidget(self.record_tree)
        self._populate_demo_tree() # 添加演示數據

        # 3. 創建右側部件和佈局
        right_widget = QFrame()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(10, 5, 10, 5)
        right_layout.setSpacing(10)

        # 3.1 詳情標題
        details_title = QLabel("出貨詳情")
        details_title_font = QFont()
        details_title_font.setPointSize(14)
        details_title_font.setBold(True)
        details_title.setFont(details_title_font)
        right_layout.addWidget(details_title)

        # 3.2 詳情表單佈局
        details_form_layout = QFormLayout()
        details_form_layout.setRowWrapPolicy(QFormLayout.DontWrapRows)
        details_form_layout.setLabelAlignment(Qt.AlignLeft)
        details_form_layout.setFormAlignment(Qt.AlignLeft | Qt.AlignTop)
        details_form_layout.setSpacing(8)
        
        self.barcode_label = QLabel("") # 使用 QLabel 顯示詳情
        self.start_time_label = QLabel("")
        self.end_time_label = QLabel("")
        self.duration_label = QLabel("")
        
        details_form_layout.addRow("條碼:", self.barcode_label)
        details_form_layout.addRow("開始時間:", self.start_time_label)
        details_form_layout.addRow("結束時間:", self.end_time_label)
        details_form_layout.addRow("持續時間:", self.duration_label)
        
        right_layout.addLayout(details_form_layout)
        right_layout.addStretch() # 將內容推到頂部

        # 4. 將左右部件添加到分割器
        main_splitter.addWidget(left_widget)
        main_splitter.addWidget(right_widget)
        main_splitter.setStretchFactor(0, 1) # 左側初始比例
        main_splitter.setStretchFactor(1, 2) # 右側初始比例
        main_splitter.setSizes([250, 550]) # 設置初始大小

        # 5. 將分割器設置為 content_frame 的佈局
        # 確保 content_frame 存在 (由 BaseContentWindow.__init__ 中的 super().__init__ 保證)
        # 清空舊佈局（如果需要）
        old_layout = self.content_frame.layout()
        if old_layout is not None:
            QWidget().setLayout(old_layout) # 簡單地解除舊佈局
        
        # 創建一個新的佈局來容納 splitter，因為 QFrame 不能直接設置 QSplitter 作為佈局
        container_layout = QVBoxLayout(self.content_frame) 
        container_layout.setContentsMargins(0,0,0,0)
        container_layout.addWidget(main_splitter)
        # self.content_frame.setLayout(container_layout) # 已經在創建時設置
        
        # --- 連接信號 (佔位符) --- 
        self.record_tree.clicked.connect(self._on_tree_item_clicked)
        self.search_input.textChanged.connect(self._apply_filters)
        self.start_date_edit.dateChanged.connect(self._apply_filters)
        self.end_date_edit.dateChanged.connect(self._apply_filters)
        self.date_filter_checkbox.stateChanged.connect(self._apply_filters)

    def _populate_demo_tree(self):
        """填充演示用的樹狀數據"""
        model = QStandardItemModel()
        root_node = model.invisibleRootItem()

        # 演示數據
        data = {
            "2025-05-02": ["10-15-30 - ITEM24680", "09-10-05 - ITEM99999"],
            "2025-05-01": ["15-45-10 - ITEM67890", "09-30-00 - ITEM12345"]
        }

        for date_str, items in data.items():
            date_item = QStandardItem(date_str)
            date_item.setEditable(False)
            font = date_item.font()
            font.setBold(True)
            date_item.setFont(font)
            root_node.appendRow(date_item)
            
            for item_str in items:
                record_item = QStandardItem(item_str)
                record_item.setEditable(False)
                # 可以將實際數據存儲在 item 中以供後續使用
                record_item.setData({"barcode": item_str.split(' - ')[-1], 
                                     "full_text": item_str,
                                     "start_time": f"{date_str} {item_str.split(' - ')[0].replace('-',':')}", # 模擬
                                     "end_time": "模擬結束時間",
                                     "duration": "模擬持續時間"}, Qt.UserRole)
                date_item.appendRow(record_item)
                
        self.record_tree.setModel(model)
        self.record_tree.expandAll() # 默認展開所有節點

    def _on_tree_item_clicked(self, index):
        """當樹項目被點擊時觸發 (佔位符)"""
        item = self.record_tree.model().itemFromIndex(index)
        if item and item.parent(): # 確保點擊的是記錄項而不是日期項
            item_data = item.data(Qt.UserRole)
            if isinstance(item_data, dict):
                self.barcode_label.setText(item_data.get("barcode", "N/A"))
                self.start_time_label.setText(item_data.get("start_time", "N/A"))
                self.end_time_label.setText(item_data.get("end_time", "N/A"))
                self.duration_label.setText(item_data.get("duration", "N/A"))
            else:
                # 清空或顯示默認值
                self.barcode_label.setText("-")
                self.start_time_label.setText("-")
                self.end_time_label.setText("-")
                self.duration_label.setText("-")
        else:
            # 點擊日期或其他項，清空詳情
            self.barcode_label.setText("")
            self.start_time_label.setText("")
            self.end_time_label.setText("")
            self.duration_label.setText("")
    
    def _apply_filters(self):
        """根據搜索框文本和日期範圍過濾樹視圖"""
        model = self.record_tree.model()
        if not model:
            return

        # 獲取篩選條件
        search_term = self.search_input.text().strip().lower()
        start_date = self.start_date_edit.date()
        end_date = self.end_date_edit.date()
        filter_by_date_range = self.date_filter_checkbox.isChecked()
        
        root = model.invisibleRootItem()

        for i in range(root.rowCount()): # 遍歷日期項
            date_item = root.child(i)
            date_index = model.indexFromItem(date_item)
            date_has_visible_child = False
            
            if not date_item: continue

            # 檢查日期是否在範圍內
            try:
                item_date = QDate.fromString(date_item.text(), "yyyy-MM-dd")
                if filter_by_date_range:
                    is_date_in_range = (start_date <= item_date <= end_date)
                else:
                    is_date_in_range = True # 如果未啟用日期篩選，則所有日期都通過
            except Exception: # 如果日期格式不對，則認為不在範圍內
                 print(f"警告: 無法解析日期項 '{date_item.text()}'")
                 is_date_in_range = False

            # 如果日期本身就不在範圍內，直接隱藏該日期及其所有子項
            if not is_date_in_range:
                self.record_tree.setRowHidden(i, QModelIndex(), True)
                continue # 處理下一個日期
                
            # 日期在範圍內，檢查子項是否匹配文本
            for j in range(date_item.rowCount()): # 遍歷記錄項
                record_item = date_item.child(j)
                if not record_item: continue
                
                # 檢查記錄項文本是否匹配
                record_text = record_item.text().lower()
                is_text_match = (not search_term) or (search_term in record_text)
                
                # 根據匹配結果顯示或隱藏記錄行
                should_hide_record = not is_text_match
                self.record_tree.setRowHidden(j, date_index, should_hide_record)
                
                if not should_hide_record:
                    date_has_visible_child = True
            
            # 如果日期項下沒有可見子項 (即使日期在範圍內)，則隱藏日期項
            should_hide_date = not date_has_visible_child
            self.record_tree.setRowHidden(i, QModelIndex(), should_hide_date)
            
            # 如果日期項本身可見，確保它是展開的
            if not should_hide_date:
                 self.record_tree.expand(date_index)
                 
        # --- 移除單獨處理搜索詞為空的部分，統一在循環內處理 --- 
        # if not search_term:
        #      for i in range(root.rowCount()):
        #          self.record_tree.setRowHidden(i, QModelIndex(), False)
        #          date_index = model.index(i, 0, QModelIndex())
        #          if date_index.isValid():
        #              self.record_tree.expand(date_index)

    # def _on_search_clicked(self):
    #     """處理搜索按鈕點擊 (待實現)"""
    #     pass
        
