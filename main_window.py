"""
主窗口模塊。

包含 MainWindow 類，作為應用程式的主要界面容器。
負責：
- 初始化和顯示主窗口 UI (標題欄、用戶信息欄、導航欄)。
- 管理子窗口的生命週期 (打開、關閉、位置保存)。
- 處理窗口拖動和釘選 (通過 WindowBehaviorMixin)。
- 處理用戶登出邏輯。
- 應用尺寸和縮放設置。
- 與 ConfigManager 交互以加載/保存設置。
- 響應 NavigationBar 的點擊事件以打開相應子窗口。
"""
from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QPushButton, 
                                QHBoxLayout, QStackedWidget, QLabel, QFrame, QToolButton, QSizePolicy)
from PySide6.QtCore import Qt, QSize, QPoint, QTimer, Signal
from PySide6.QtGui import QIcon, QFont, QPixmap, QMouseEvent, QCloseEvent
import os
import json
from pages.shipment_record_page import ShipmentRecordWindow 
from pages.process_shipment_page import ProcessShipmentWindow
from pages.subscription_page import SubscriptionWindow
from pages.settings_page import SettingsWindow
from PySide6.QtWidgets import QApplication
from ui_constants import *
from navigation_bar import NavigationBar
from window_behavior_mixin import WindowBehaviorMixin
from config_manager import ConfigManager

# --- 新增：窗口類型到類的映射 --- 
WINDOW_CLASS_MAP = {
    "shipment_record": ShipmentRecordWindow,
    "process_shipment": ProcessShipmentWindow,
    "subscription": SubscriptionWindow,
    "settings": SettingsWindow, # SettingsWindow 可能需要特殊處理
}
# --- 結束新增 ---

class MainWindow(QMainWindow, WindowBehaviorMixin):
    logout_signal = Signal()

    def __init__(self, user_data=None):
        super().__init__()
        self.user_data = user_data or {"username": "未知"}
        
        self.setWindowFlag(Qt.FramelessWindowHint)
        
        self.config_manager = ConfigManager()
        
        self.open_windows = {}
        self.size_ratios = SIZE_RATIOS
        self.fixed_window_widths = FIXED_WINDOW_WIDTHS
        
        self.size_setting = self.config_manager.load_size_setting()
        pos_x, pos_y = self.config_manager.load_window_position()
        
        self.setup_base_ui()
        
        self.setup_window_behavior(header_height=30, pin_button=self.pin_btn)
        
        self.move(pos_x, pos_y)

        if hasattr(self, 'navigation_bar'):
            self.navigation_bar.nav_item_clicked.connect(self.handle_open_window)
            if hasattr(self.navigation_bar, 'logout_requested'):
                 self.navigation_bar.logout_requested.connect(self.logout)
        
        self.apply_size_setting(self.size_setting, is_initial=True)
        
    def setup_base_ui(self):
        self.setWindowTitle("出貨記錄")
        self.setStyleSheet("""
            QMainWindow { background-color: #f0f0f0; font-family: Arial; border: 1px solid #cccccc; }
            QFrame#header { background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #4a86e8, stop:1 #5f9ea0); color: white; }
            QPushButton#pinBtn { color: white; background: transparent; border: none; font-weight: bold; }
            QPushButton#pinBtn[pinned="true"] { color: #ffff99; }
            QPushButton#pinBtn:hover { color: #ccccff; }
            QLabel#headerTitle { color: white; font-size: 16px; font-weight: bold; }
            QLabel#userLabel { color: white; font-size: 12px; }
            QPushButton#logoutBtn { color: white; background: transparent; border: none; font-size: 12px; }
            QPushButton#logoutBtn:hover { text-decoration: underline; }
            QPushButton#minimizeBtn, QPushButton#closeBtn { color: white; background: transparent; border: none; font-size: 16px; font-weight: bold; }
            QPushButton#minimizeBtn:hover, QPushButton#closeBtn:hover { color: #dddddd; }
        """)
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        header = QFrame()
        header.setObjectName("header")
        header.setFixedHeight(30)
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(10, 0, 5, 0)
        header_layout.setSpacing(5)
        
        self.pin_btn = QPushButton()
        self.pin_btn.setObjectName("pinBtn")
        self.pin_btn.setFixedSize(20, 20)
        self.pin_btn.setIcon(QIcon("icons/未釘選.svg"))
        self.pin_btn.setIconSize(QSize(16, 16))
        self.pin_btn.setProperty("pinned", "false")
        
        self.header_title_label = QLabel("出貨記錄")
        self.header_title_label.setObjectName("headerTitle")
        
        minimize_button = QPushButton("−")
        minimize_button.setObjectName("minimizeBtn")
        minimize_button.setFixedSize(20, 20)
        minimize_button.clicked.connect(self.showMinimized)

        close_button = QPushButton("✕")
        close_button.setObjectName("closeBtn")
        close_button.setFixedSize(20, 20)
        close_button.clicked.connect(self.close)
        
        header_layout.addWidget(self.pin_btn)
        header_layout.addWidget(self.header_title_label)
        header_layout.addStretch()
        header_layout.addWidget(minimize_button)
        header_layout.addWidget(close_button)
        main_layout.addWidget(header)
        
        user_bar = QFrame()
        user_bar.setObjectName("header")
        user_bar.setFixedHeight(25)
        user_bar_layout = QHBoxLayout(user_bar)
        user_bar_layout.setContentsMargins(10, 0, 10, 0)
        user_bar_layout.setSpacing(5)
        
        user_label = QLabel(f"用戶: {self.user_data.get('username', '未知')}")
        user_label.setObjectName("userLabel")
        
        logout_button = QPushButton("登出")
        logout_button.setObjectName("logoutBtn")
        logout_button.clicked.connect(self.logout)
        
        user_bar_layout.addWidget(user_label)
        user_bar_layout.addStretch()
        user_bar_layout.addWidget(logout_button)
        main_layout.addWidget(user_bar)
        
        content_container = QWidget()
        content_layout = QVBoxLayout(content_container)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        self.navigation_bar = NavigationBar()
        content_layout.addWidget(self.navigation_bar)
        
        main_layout.addWidget(content_container, 1)
        self.setCentralWidget(central_widget)
    
    def apply_size_setting(self, size, is_initial=False):
        """應用尺寸設置到主窗口和導航欄，並更新主窗口標題可見性"""
        if not is_initial:
            self.config_manager.save_size_setting(size)
        self.size_setting = size

        if hasattr(self, 'header_title_label'):
            if size in ["default", "small"]:
                self.header_title_label.setVisible(False)
            else:
                self.header_title_label.setVisible(True)

        fixed_width = FIXED_WINDOW_WIDTHS.get(size, FIXED_WINDOW_WIDTHS["default"])
        self.setFixedWidth(fixed_width)

        if hasattr(self, 'navigation_bar'):
            self.navigation_bar.update_layout(size)

        offset_x = self.frameGeometry().left() + self.frameGeometry().width() + 30
        for window in self.open_windows.values():
             if window and window.isVisible():
                 current_pos = window.pos()
                 window.move(offset_x, current_pos.y())

        self.update()
        if self.centralWidget() and self.centralWidget().layout():
            self.centralWidget().layout().invalidate()
            self.centralWidget().layout().activate()
            
        QTimer.singleShot(0, self.adjustSize)
        
        QApplication.processEvents()
    
    def logout(self):
        """登出處理 - 關閉應用程式"""
        print("登出請求 - 關閉應用程式")
        self.logout_signal.emit()
        self.close()
            
    def closeEvent(self, event: QCloseEvent):
        """覆寫關閉事件，儲存位置並關閉子窗口 (使用 ConfigManager)"""
        print("主窗口關閉事件觸發")
        self.config_manager.save_window_position(self.pos())
        # --- 修改: 在關閉子窗口前更新所有按鈕狀態 --- 
        if hasattr(self, 'navigation_bar'):
            for window_type in self.open_windows.keys():
                if self.open_windows[window_type] and self.open_windows[window_type].isVisible():
                    # 保存位置
                    print(f"關閉主窗口時保存窗口 {window_type} 位置: {self.open_windows[window_type].pos()}")
                    self.config_manager.save_child_window_position(window_type, self.open_windows[window_type].pos())
                    # 更新按鈕狀態
                    self.navigation_bar.set_button_active(window_type, False)
        # --- 結束修改 ---
            
        # 現在關閉所有子窗口
        for window_type, window in list(self.open_windows.items()): # 使用 list 創建副本以安全迭代
             # if window and window.isVisible(): # 位置和狀態已在上面處理
                 # print(f"關閉主窗口時保存窗口 {window_type} 位置: {window.pos()}")
                 # self.config_manager.save_child_window_position(window_type, window.pos())
             if window:
                 # --- 新增: 嘗試斷開連接以避免殘留信號處理 --- 
                 # try:
                 #     window.destroyed.disconnect(self.on_destroyed)
                 # except (RuntimeError, TypeError): # 可能已斷開或對象已刪除
                 #     pass
                 # --- 結束新增 ---
                 # --- 移除上面的 disconnect --- 
                 window.close()
        self.open_windows.clear()
        super().closeEvent(event) 

    # --- 修改: 移除 on_destroyed 中的按鈕狀態更新 --- 
    def on_destroyed(self, obj=None, wtype=None):
        # 這個函數現在只負責從 open_windows 字典中移除記錄
        # 按鈕狀態由 handle_open_window (關閉時) 和 closeEvent (主窗口關閉時) 處理
        if wtype:
            print(f"窗口 {wtype} destroyed 信號觸發")
            if wtype in self.open_windows and self.open_windows[wtype] is obj:
                print(f"從字典中移除窗口記錄: {wtype}")
                del self.open_windows[wtype]
            # else:
                # print(f"警告: 嘗試移除不存在或不匹配的窗口記錄 {wtype}")
        # --- 移除下面的代碼 --- 
        # if hasattr(self, 'navigation_bar'):
        #     self.navigation_bar.set_button_active(wtype, False)
        # --- 結束移除 ---

    def handle_open_window(self, window_type):
        """處理打開/關閉窗口的請求 (使用 ConfigManager)"""
        print(f"處理窗口請求: {window_type}")
        if window_type in self.open_windows and self.open_windows[window_type] and self.open_windows[window_type].isVisible():
            window_to_close = self.open_windows[window_type]
            print(f"關閉現有窗口: {window_type} at {window_to_close.pos()}")
            self.config_manager.save_child_window_position(window_type, window_to_close.pos())
            
            # --- 新增: 關閉前嘗試斷開連接 --- 
            # try:
            #     window_to_close.destroyed.disconnect(self.on_destroyed)
            # except (RuntimeError, TypeError):
            #     pass
            # --- 結束新增 ---
            # --- 移除上面的 disconnect --- 
            
            window_to_close.close()
            if window_type in self.open_windows:
                 del self.open_windows[window_type]
            # --- 新增: 更新按鈕狀態 --- 
            if hasattr(self, 'navigation_bar'):
                self.navigation_bar.set_button_active(window_type, False)
            # --- 結束新增 ---
            return

        saved_pos = self.config_manager.load_child_window_position(window_type)

        if saved_pos:
            target_x = saved_pos.x()
            target_y = saved_pos.y()
            print(f"找到 {window_type} 保存的位置: ({target_x}, {target_y})")
        else:
            visible_count = len([win for win in self.open_windows.values() if win and win.isVisible()])
            main_window_rect = self.frameGeometry()
            target_x = main_window_rect.left() + main_window_rect.width() + 30
            target_y = main_window_rect.top() + (visible_count * 20)
            print(f"未找到 {window_type} 保存的位置，計算默認位置: ({target_x}, {target_y})")

        window = None
        try:
            # --- 修改：使用映射查找類 --- 
            window_class = WINDOW_CLASS_MAP.get(window_type)
            if window_class:
                # 檢查是否是需要特殊參數的 SettingsWindow
                if window_type == "settings":
                    window = window_class(window_type=window_type)
                    if window: # 確保 window 成功創建
                        window.size_setting_changed.connect(self.apply_size_setting)
                # --- 新增: 處理 SubscriptionWindow --- 
                elif window_type == "subscription":
                    # 傳遞 user_data 給 SubscriptionWindow
                    window = window_class(window_type=window_type, user_data=self.user_data)
                # --- 結束新增 ---
                else:
                    window = window_class(window_type=window_type) # 其他窗口標準初始化
            # --- 結束修改 ---
            else:
                print(f"錯誤: 未知的窗口類型 '{window_type}'")
                return
        except Exception as e:
            print(f"創建窗口 '{window_type}' 時出錯: {e}")
            return

        if window:
            # --- 修改: 重命名回調函數以反映其主要職責 --- 
            def remove_window_from_dict(obj=None, wtype=window_type):
                self.on_destroyed(obj, wtype)
            # --- 結束修改 ---
            
            # 連接 destroyed 信號到新的回調
            window.destroyed.connect(remove_window_from_dict)

            window.move(target_x, target_y)
            window.show()
                
            self.open_windows[window_type] = window
            # --- 新增: 更新按鈕狀態 --- 
            if hasattr(self, 'navigation_bar'):
                self.navigation_bar.set_button_active(window_type, True)
            # --- 結束新增 ---
            print(f"窗口 {window_type} 已創建並顯示於 ({target_x}, {target_y})")
            print(f"當前打開的窗口: {list(self.open_windows.keys())}")
        else:
            print(f"未能創建窗口: {window_type}") 