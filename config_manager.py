"""
配置管理模塊。

包含 ConfigManager 類，負責從 JSON 文件 ('settings.json') 加載和保存應用程式設置。
提供讀取和寫入特定設置項 (如窗口位置、尺寸) 的便捷方法。
使用緩存機制以減少文件 I/O 次數。
"""
import json
import os
from PySide6.QtCore import QPoint # 需要 QPoint 來反序列化

class ConfigManager:
    def __init__(self, settings_file="settings.json"):
        """初始化 ConfigManager。

        Args:
            settings_file (str): 設置文件的路徑。
        """
        self.settings_file = settings_file
        self.settings_cache = self._load_settings() # 加載時緩存

    def _load_settings(self):
        """從 JSON 文件加載設置。如果文件不存在或無效，返回空字典。"""
        if os.path.exists(self.settings_file):
            try:
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if not content:
                        return {}
                    # 注意: 這裡可以添加更複雜的反序列化邏輯，例如將 {'x': 10, 'y': 10} 轉回 QPoint
                    # 但為了保持 ConfigManager 通用，我們暫時只返回原始字典
                    return json.loads(content)
            except (json.JSONDecodeError, IOError) as e:
                print(f"警告: 無法加載設置文件 '{self.settings_file}': {e}")
                return {} 
        return {}

    def _save_settings(self):
        """將緩存的設置保存到 JSON 文件。"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                # 注意: 如果設置中包含非 JSON 原生類型 (如 QPoint)，需要自定義序列化器
                # 為了簡單起見，我們假設存儲的都是基本類型或嵌套字典/列表
                json.dump(self.settings_cache, f, indent=4)
            return True
        except IOError as e:
            print(f"錯誤: 無法保存設置文件 '{self.settings_file}': {e}")
            return False

    def get_setting(self, key, default=None):
        """從緩存的設置中獲取一個值。

        Args:
            key (str): 設置項的鍵。
            default: 如果鍵不存在時返回的默認值。

        Returns:
            設置項的值或默認值。
        """
        return self.settings_cache.get(key, default)

    def set_setting(self, key, value):
        """更新緩存中的一個設置項並立即保存到文件。

        Args:
            key (str): 設置項的鍵。
            value: 要設置的新值。
        """
        self.settings_cache[key] = value
        self._save_settings() # 每次設置後立即保存

    def get_all_settings(self):
        """獲取所有緩存的設置。"""
        return self.settings_cache.copy() # 返回副本以防外部修改緩存

    def refresh_cache(self):
        """從文件重新加載設置到緩存。"""
        self.settings_cache = self._load_settings()
        
    # --- 特定設置的便捷方法 (可選，但可以封裝複雜邏輯) ---
    
    def load_window_position(self, default_pos=(10, 10)):
        """加載主窗口位置"""
        try:
            pos_x = self.get_setting("window_pos_x", default_pos[0])
            pos_y = self.get_setting("window_pos_y", default_pos[1])
            return int(pos_x), int(pos_y)
        except (ValueError, TypeError):
            return default_pos

    def save_window_position(self, pos: QPoint):
        """保存主窗口位置"""
        self.set_setting("window_pos_x", pos.x())
        self.set_setting("window_pos_y", pos.y())

    def load_child_window_position(self, window_type):
        """加載子窗口位置"""
        positions = self.get_setting("child_window_positions", {})
        pos_data = positions.get(window_type)
        if pos_data and 'x' in pos_data and 'y' in pos_data:
            try:
                return QPoint(int(pos_data['x']), int(pos_data['y']))
            except (ValueError, TypeError):
                return None
        return None

    def save_child_window_position(self, window_type, pos: QPoint):
        """保存子窗口位置"""
        positions = self.get_setting("child_window_positions", {})
        positions[window_type] = {'x': pos.x(), 'y': pos.y()}
        self.set_setting("child_window_positions", positions) # 保存更新後的整個字典
        
    def load_size_setting(self, default="default"):
        """加載尺寸設置"""
        return self.get_setting("size", default)
        
    def save_size_setting(self, size):
        """保存尺寸設置"""
        self.set_setting("size", size) 