"""
登入窗口模塊。

包含 LoginWindow 類，負責處理用戶登入界面的 UI、
獲取用戶輸入、驗證憑證 (模擬) 以及在成功登入後啟動主窗口。
"""
import uuid
import platform
import subprocess
from PySide6.QtWidgets import (QWidget, QLabel, QLineEdit, QPushButton, 
                              QVBoxLayout, QHBoxLayout, QMessageBox, QFrame)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QFont, QIcon
from main_window import MainWindow

class LoginWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("出貨記錄系統 - 登入")
        self.setFixedSize(450, 380)
        self.setStyleSheet("""
            QWidget {
                background-color: #f5f5f5;
                font-family: Arial;
            }
            QLabel { /* General labels */
                color: #333333;
                font-size: 14px; /* Keep for username/password labels */
            }
            #loginTitleLabel { /* Specific style for the title */
                color: #333333; /* Keep color consistent */
                font-size: 28px; /* <<< Ensure title size */
                font-weight: bold;
            }
            QLineEdit {
                padding: 12px;
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: white;
                font-size: 14px;
                min-height: 25px;
            }
            QPushButton { /* Login button */
                background-color: #4a86e8;
                color: white;
                border: none;
                padding: 14px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 20px; /* <<< Increase button font size */
            }
            QPushButton:hover {
                background-color: #3a76d8;
            }
        """)
        
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(50, 50, 50, 50)
        main_layout.setSpacing(25)
        
        # Title
        title_label = QLabel("出貨記錄系統")
        title_label.setObjectName("loginTitleLabel")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # Username
        username_layout = QVBoxLayout()
        username_layout.setSpacing(8)
        username_label = QLabel("使用者名稱:")
        self.username_input = QLineEdit()
        self.username_input.setText("admin")  # Default username
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        main_layout.addLayout(username_layout)

        # Add extra spacing before the password layout
        main_layout.addSpacing(10)

        # Password
        password_layout = QVBoxLayout()
        password_layout.setSpacing(8)
        password_label = QLabel("密碼:")
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setText("password")  # Default password
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        main_layout.addLayout(password_layout)
        
        # Add extra spacing before the login button
        main_layout.addSpacing(15)

        # Login button
        self.login_button = QPushButton("登入")
        self.login_button.clicked.connect(self.login)
        main_layout.addWidget(self.login_button)
        
        self.setLayout(main_layout)
        
    def login(self):
        username = self.username_input.text()
        password = self.password_input.text()
        
        # Simple validation for demonstration
        if username and password:
            # Get MAC address and other system info for unique login check
            mac = self.get_mac_address()
            system_info = platform.platform()
            board_serial = self.get_board_serial()
            
            # Here you would typically make an API call to verify subscription and login status
            self.verify_subscription(username, mac, board_serial, system_info)
        else:
            QMessageBox.warning(self, "登入錯誤", "請輸入用戶名和密碼")
    
    def get_mac_address(self):
        # Get MAC address (platform independent way)
        mac = ':'.join(['{:02x}'.format((uuid.getnode() >> ele) & 0xff)
                      for ele in range(0, 8*6, 8)][::-1])
        return mac
    
    def get_board_serial(self):
        """獲取主機板序號 (跨平台嘗試)"""
        system = platform.system()
        serial_number = "Unknown"
        try:
            if system == "Windows":
                # command = "wmic baseboard get SerialNumber"
                # output = subprocess.check_output(command, shell=True, text=True, stderr=subprocess.DEVNULL)
                # lines = output.strip().split('\n')
                # if len(lines) > 1:
                #     serial_number = lines[1].strip()
                # 使用 PowerShell 命令，更穩定且避免亂碼
                command = "powershell.exe Get-CimInstance -ClassName Win32_BaseBoard | Select-Object -ExpandProperty SerialNumber"
                output = subprocess.check_output(command, shell=True, text=True, stderr=subprocess.DEVNULL)
                serial_number = output.strip()
            elif system == "Linux":
                # 嘗試讀取文件，避免 sudo
                try:
                    with open("/sys/class/dmi/id/board_serial", "r") as f:
                        serial_number = f.read().strip()
                except FileNotFoundError:
                    # 如果文件不存在，嘗試 dmidecode (可能需要權限)
                    try:
                        command = "sudo dmidecode -s baseboard-serial-number"
                        output = subprocess.check_output(command, shell=True, text=True, stderr=subprocess.DEVNULL)
                        serial_number = output.strip()
                    except Exception as e:
                        print(f"獲取 Linux 主板序號 (dmidecode) 失敗: {e}")
            elif system == "Darwin": # macOS
                command = "ioreg -l | grep IOPlatformSerialNumber"
                output = subprocess.check_output(command, shell=True, text=True, stderr=subprocess.DEVNULL)
                lines = output.strip().split('\n')
                if lines:
                    # 輸出格式通常是 "    | |   "IOPlatformSerialNumber" = "XXXXXXXXXXX" "
                    parts = lines[0].split('"')
                    if len(parts) >= 4:
                        serial_number = parts[-2] # 取倒數第二個引號內的內容
        except Exception as e:
            print(f"獲取主板序號時發生錯誤 ({system}): {e}")
            serial_number = "Error"
            
        # 如果無法獲取或出錯，返回一個標識符
        if not serial_number or serial_number == "Unknown":
            return "N/A"
        return serial_number
    
    def verify_subscription(self, username, mac, board_serial, system_info):
        # Mock verification - in a real app, you would check with a server
        # For demo purposes, we'll just proceed to the main window
        print(f"驗證使用者: {username}")
        print(f"MAC 地址: {mac}")
        print(f"主機板序號: {board_serial}")
        print(f"系統資訊: {system_info}")
        
        # 創建用戶數據字典
        user_data = {
            "username": username,
            "mac_address": mac,
            "board_serial": board_serial,
            "system_info": system_info
            # 您可以在此處添加從伺服器驗證獲取的其他用戶信息
        }
        
        # Open main window and close login window
        # 傳遞 user_data 給 MainWindow
        self.main_window = MainWindow(user_data=user_data) 
        self.main_window.show()
        self.close() 