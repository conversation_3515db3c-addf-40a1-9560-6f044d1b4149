"""
窗口行為 Mixin 模塊。

包含 WindowBehaviorMixin 類，提供可重用的窗口拖動和釘選功能。
可以被 QMainWindow 或 QWidget 子類繼承以快速添加這些行為。
"""
from PySide6.QtCore import Qt, QPoint
from PySide6.QtGui import QIcon, QMouseEvent
from PySide6.QtWidgets import QPushButton

class WindowBehaviorMixin:
    """
    實現窗口拖動和釘選的混合類
    可同時用於 MainWindow 和 BaseContentWindow
    """
    
    def setup_window_behavior(self, header_height=30, pin_button=None):
        """
        初始化窗口行為
        
        Args:
            header_height: 可拖動標題區域的高度
            pin_button: 釘選按鈕對象，如果為 None 則嘗試使用 self.pin_button 或 self.pin_btn
        """
        # 拖動相關屬性
        self.dragging = False
        self.drag_position = QPoint()
        self.header_height = header_height # 存儲標題欄高度
        
        # 釘選相關屬性
        self.is_pinned = False
        
        # 確定要使用的釘選按鈕
        if pin_button:
            self._pin_button_ref = pin_button
        elif hasattr(self, 'pin_button'): # BaseContentWindow 使用 pin_button
             self._pin_button_ref = self.pin_button
        elif hasattr(self, 'pin_btn'): # MainWindow 使用 pin_btn
             self._pin_button_ref = self.pin_btn
        else:
            self._pin_button_ref = None
            print("警告: 未找到可用的釘選按鈕 (pin_button 或 pin_btn)")

        # 如果找到釘選按鈕，連接信號
        if self._pin_button_ref:
            self._pin_button_ref.clicked.connect(self.toggle_pin)
            # 根據初始 is_pinned 狀態設置圖標 (雖然通常是 False)
            self._update_pin_button_ui() 
    
    def toggle_pin(self):
        """切換窗口釘選狀態"""
        self.is_pinned = not self.is_pinned
        
        # 更新窗口置頂標誌
        flags = self.windowFlags()
        if self.is_pinned:
            self.setWindowFlags(flags | Qt.WindowStaysOnTopHint)
        else:
            self.setWindowFlags(flags & ~Qt.WindowStaysOnTopHint)
        
        # 更新釘選按鈕 UI
        self._update_pin_button_ui()
        
        # 重新顯示窗口以應用標誌更改
        self.show()
    
    def _update_pin_button_ui(self):
        """更新釘選按鈕的外觀 (圖標和屬性)"""
        if self._pin_button_ref:
            icon_path = "icons/已釘選.svg" if self.is_pinned else "icons/未釘選.svg"
            self._pin_button_ref.setIcon(QIcon(icon_path))
            self._pin_button_ref.setProperty("pinned", str(self.is_pinned).lower())
            
            # 刷新按鈕樣式以應用屬性選擇器
            self._pin_button_ref.style().polish(self._pin_button_ref)

    def mousePressEvent(self, event: QMouseEvent):
        """處理鼠標按下事件 (用於拖動)"""
        # 檢查是否在標題欄區域內按下左鍵
        if event.button() == Qt.LeftButton and event.y() < self.header_height:
            self.dragging = True
            # 計算鼠標全局位置與窗口左上角的偏移
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
        else:
            # 如果事件不是用於拖動，調用父類的處理方法 (如果有的話)
            if hasattr(super(), 'mousePressEvent'):
                 super().mousePressEvent(event)
            # else:
                 # event.ignore() # QWidget 默認會忽略，不需要顯式調用
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """處理鼠標釋放事件 (用於拖動)"""
        if event.button() == Qt.LeftButton:
            self.dragging = False
            event.accept()
        else:
            if hasattr(super(), 'mouseReleaseEvent'):
                 super().mouseReleaseEvent(event)
            # else:
                 # event.ignore()
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """處理鼠標移動事件，實現窗口拖動"""
        # 僅在拖動狀態且左鍵被按下時移動窗口
        if self.dragging and event.buttons() & Qt.LeftButton:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
        else:
            if hasattr(super(), 'mouseMoveEvent'):
                 super().mouseMoveEvent(event)
            # else:
                 # event.ignore() 