"""
導航欄組件模塊。

包含 NavigationBar 類，負責創建和管理主窗口左側的導航按鈕、
顯示訂閱信息和登出按鈕。

發射 nav_item_clicked 信號以通知主窗口哪個導航項被點擊。
(在當前版本中，登出按鈕直接調用 MainWindow 的方法，而非通過信號)
根據不同的尺寸設置調整自身的佈局和樣式。
"""
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame, QSizePolicy
)
from PySide6.QtCore import Qt, QSize, Signal
from PySide6.QtGui import QIcon, QFont, QPixmap
import os
from ui_constants import *

# --- 新增: 活躍按鈕背景色 ---
ACTIVE_BUTTON_COLOR = "#e0eafc" # 淺藍色
ACTIVE_BUTTON_HOVER_COLOR = "#d0dffc" # 懸停時稍深的藍色
# --- 結束新增 ---

class NavigationBar(QWidget):
    # 定義信號，當導航項被點擊時發射，參數為 window_type (str)
    nav_item_clicked = Signal(str)
    logout_requested = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.size_setting = "default" # 儲存當前的尺寸設置
        self.size_ratios = SIZE_RATIOS # 引用常數
        self.button_frames = {} # <--- 新增: 儲存按鈕框架的映射
        self._init_ui()

    def _init_ui(self):
        # 主佈局 (垂直)
        main_nav_layout = QVBoxLayout(self)
        main_nav_layout.setContentsMargins(0, 0, 0, 0)
        main_nav_layout.setSpacing(0)
        self.setObjectName("nav_container") # 設置對象名以應用樣式

        # 頂部導航按鈕容器
        self.nav_layout = QVBoxLayout()
        self.nav_layout.setAlignment(Qt.AlignTop)
        
        # 創建導航按鈕 (初始狀態，稍後由 update_layout 配置)
        self.shipment_record_btn = self._create_nav_item("出貨記錄", "shipment_record")
        self.process_shipment_btn = self._create_nav_item("進行出貨", "process_shipment")
        self.subscription_btn = self._create_nav_item("服務訂閱", "subscription")
        self.settings_btn = self._create_nav_item("系統設定", "settings")
        
        for btn in [self.shipment_record_btn, self.process_shipment_btn, self.subscription_btn, self.settings_btn]:
            self.nav_layout.addWidget(btn)

        # 底部容器
        self.bottom_container = QFrame()
        self.bottom_container.setObjectName("bottom_container")
        self.bottom_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.bottom_layout = QVBoxLayout(self.bottom_container)
        self.bottom_layout.setContentsMargins(0, 0, 0, 0) # 邊距由 apply_size_setting 控制

        # 訂閱信息標籤
        self.subscription_label = QLabel()
        self.subscription_label.setObjectName("subscription")
        self.subscription_label.setAlignment(Qt.AlignCenter)
        # 初始文本，稍後由 update_layout 更新
        self.subscription_label.setText("出貨輔助 Pro<br><span style='font-size: 12px; font-weight: normal;'>(...)</span>")
        self.subscription_label.setTextFormat(Qt.RichText)

        # 登出按鈕
        self.logout_btn = QPushButton("登出")
        self.logout_btn.setObjectName("logout")
        self.logout_btn.clicked.connect(self.logout_requested.emit)

        # --- 修改: 使用嵌套 QHBoxLayout 和 stretch 居中 --- 
        # self.bottom_layout.addWidget(self.subscription_label, 0, Qt.AlignHCenter)
        # self.bottom_layout.addWidget(self.logout_btn, 0, Qt.AlignHCenter)
        
        # 訂閱標籤的居中佈局
        sub_label_centering_layout = QHBoxLayout()
        sub_label_centering_layout.addStretch(1)
        sub_label_centering_layout.addWidget(self.subscription_label)
        sub_label_centering_layout.addStretch(1)
        self.bottom_layout.addLayout(sub_label_centering_layout)
        
        # 登出按鈕的居中佈局
        logout_btn_centering_layout = QHBoxLayout()
        logout_btn_centering_layout.addStretch(1)
        logout_btn_centering_layout.addWidget(self.logout_btn)
        logout_btn_centering_layout.addStretch(1)
        self.bottom_layout.addLayout(logout_btn_centering_layout)
        # --- 結束修改 ---

        # 將頂部按鈕佈局和底部容器添加到主佈局
        main_nav_layout.addLayout(self.nav_layout)
        main_nav_layout.addStretch(1) # 添加彈簧將底部推到底部
        main_nav_layout.addWidget(self.bottom_container)

        # --- 新增: 應用基礎按鈕樣式 (包含 active 狀態) ---
        self.update_button_base_style()
        # --- 結束新增 ---

    def update_layout(self, size_setting):
        """根據新的尺寸設置更新導航欄佈局和元素"""
        self.size_setting = size_setting

        # 配置導航欄頂部佈局 (間距, 邊距)
        nav_spacing = NAV_SPACING.get(size_setting, NAV_SPACING["default"])
        nav_margins = NAV_MARGINS.get(size_setting, NAV_MARGINS["default"])
        self.nav_layout.setSpacing(nav_spacing)
        self.nav_layout.setContentsMargins(*nav_margins)

        # 配置導航按鈕
        self._configure_nav_item(self.shipment_record_btn, "出貨記錄")
        self._configure_nav_item(self.process_shipment_btn, "進行出貨")
        self._configure_nav_item(self.subscription_btn, "服務訂閱")
        self._configure_nav_item(self.settings_btn, "系統設定")

        # 配置底部佈局 (間距, 邊距)
        bottom_spacing = BOTTOM_SPACING.get(size_setting, BOTTOM_SPACING["default"])
        self.bottom_layout.setSpacing(bottom_spacing)
        self.bottom_layout.setContentsMargins(0, 0, 0, BOTTOM_LAYOUT_BOTTOM_MARGIN)

        # 配置登出按鈕
        logout_btn_height = LOGOUT_BTN_HEIGHT.get(size_setting, LOGOUT_BTN_HEIGHT["default"])
        self.logout_btn.setFixedSize(LOGOUT_BTN_WIDTH, logout_btn_height)
        padding = "2px" if size_setting == "small" else "8px"
        base_style = (
            f"QPushButton#logout {{" # 使用雙括號轉義
            f"    background-color: white; color: #333333; border: 1px solid #cccccc;"
            f"    border-radius: 4px; min-width: {LOGOUT_BTN_WIDTH}px; max-width: {LOGOUT_BTN_WIDTH}px;"
            f"    font-weight: bold; font-size: 16px; margin: 0px; padding: {padding};"
            f"}}"
            f"QPushButton#logout:hover {{ background-color: #f1f1f1; }}"
        )
        self.logout_btn.setStyleSheet(base_style)

        # 配置訂閱標籤字體和文本
        sub_font_size = SUBSCRIPTION_FONT_SIZES.get(size_setting, SUBSCRIPTION_FONT_SIZES["default"])
        current_font = self.subscription_label.font()
        current_font.setPointSize(sub_font_size)
        current_font.setStyleStrategy(QFont.PreferAntialias)
        self.subscription_label.setFont(current_font)
        if size_setting == "small":
            second_line_size_px = 10
        elif size_setting == "medium":
            second_line_size_px = 14
        elif size_setting == "large":
            second_line_size_px = 15
        else: # default
            second_line_size_px = 12
        expiry_text = "(150天後到期)" # 實際應從外部傳入或獲取
        rich_text = f"出貨輔助 Pro<br><span style='font-size: {second_line_size_px}px; font-weight: normal;'>{expiry_text}</span>"
        self.subscription_label.setText(rich_text)
        
        # --- 新增: 設置 NavigationBar 自身的高度 --- 
        nav_bar_height = NAV_HEIGHTS.get(size_setting, NAV_HEIGHTS["default"])
        self.setFixedHeight(nav_bar_height)
        # --- 結束新增 ---
        
        # 觸發佈局更新 (可選，但有助於確保一致性)
        self.layout().invalidate()
        self.layout().activate()
        self.updateGeometry()

        # --- 新增: 尺寸改變時也可能需要更新樣式 (如果樣式依賴尺寸) ---
        # 在這個案例中，active 樣式不依賴尺寸，所以可能不需要下面這行
        # self.update_button_base_style()
        # --- 結束新增 ---

    def _create_nav_item(self, icon_name, window_type):
        """創建導航按鈕框架 (不包含尺寸配置)"""
        frame = QFrame()
        frame.setObjectName("nav_button")
        frame.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        layout = QVBoxLayout(frame)

        icon_label = QLabel()
        icon_label.setObjectName("icon_label")
        icon_label.setAlignment(Qt.AlignCenter)
        text_label = QLabel(icon_name)
        text_label.setObjectName("button_text")
        text_label.setAlignment(Qt.AlignHCenter)
        text_label.setWordWrap(False)

        layout.addStretch(1)
        layout.addWidget(icon_label, 0, Qt.AlignHCenter)
        layout.addWidget(text_label, 0, Qt.AlignHCenter)
        layout.addStretch(1)

        # 連接點擊事件發射信號
        frame.mousePressEvent = lambda event, wtype=window_type: self._handle_nav_click(event, wtype)

        # --- 新增: 將框架添加到映射中 ---
        self.button_frames[window_type] = frame
        # --- 結束新增 ---
        return frame

    def _handle_nav_click(self, event, window_type):
        """處理導航項點擊並發射信號"""
        if event.button() == Qt.LeftButton:
            self.nav_item_clicked.emit(window_type)

    def _get_nav_item_size(self):
        ratio = self.size_ratios.get(self.size_setting, 1.0)
        return int(NAV_ITEM_BASE_SIZE * ratio)
            
    def _get_icon_size(self):
        ratio = self.size_ratios.get(self.size_setting, 1.0)
        return int(ICON_BASE_SIZE * ratio)

    def _configure_nav_item(self, frame, icon_name):
        """根據當前尺寸設置配置單個導航項目"""
        frame_size = self._get_nav_item_size()
        extra_height = NAV_ITEM_EXTRA_HEIGHT.get(self.size_setting, NAV_ITEM_EXTRA_HEIGHT["default"])
        icon_size = self._get_icon_size()
        font_size = NAV_FONT_SIZES.get(self.size_setting, NAV_FONT_SIZES["default"])
        calculated_height = frame_size + extra_height

        frame.setFixedHeight(calculated_height)
        # --- 移除在這裡單獨設置的 frame.setStyleSheet --- 
        # frame_style = f"QFrame#nav_button {{ padding: 0px; background-color: transparent; border-radius: 5px; }}"
        # frame_style += f" QFrame#nav_button:hover {{ background-color: rgba(0, 0, 0, 0.05); }}"
        # frame.setStyleSheet(frame_style)
        # --- 結束移除 ---

        icon_path = os.path.join("icons", f"{icon_name}.svg")
        text_label_widget = None
        for child in frame.findChildren(QLabel):
            if child.objectName() == "icon_label":
                if os.path.exists(icon_path):
                    pixmap = QPixmap(icon_path)
                    pixmap = pixmap.scaled(icon_size, icon_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    child.setPixmap(pixmap)
                    child.setFixedSize(icon_size, icon_size)
            elif child.objectName() == "button_text":
                text_label_widget = child

        if text_label_widget:
            font = QFont()
            font.setPointSize(font_size)
            font.setBold(True)
            font.setStyleStrategy(QFont.PreferAntialias)
            text_label_widget.setFont(font)
            text_label_widget.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
            text_label_widget.setMinimumWidth(icon_size)

        layout = frame.layout()
        if layout:
            margins = NAV_ITEM_MARGINS.get(self.size_setting, NAV_ITEM_MARGINS["default"])
            spacing = NAV_ITEM_SPACING.get(self.size_setting, NAV_ITEM_SPACING["default"])
            layout.setContentsMargins(*margins)
            layout.setSpacing(spacing) 

    # --- 新增: 更新基礎按鈕樣式的方法 ---
    def update_button_base_style(self):
        frame_style = f"""
            QFrame#nav_button {{
                padding: 0px;
                background-color: transparent;
                border-radius: 5px;
                border: 1px solid transparent; /* Add transparent border for consistency */
            }}
            QFrame#nav_button:hover {{
                background-color: rgba(0, 0, 0, 0.05);
                border: 1px solid #cccccc; /* Show border on hover */
            }}
            QFrame#nav_button[active="true"] {{
                background-color: {ACTIVE_BUTTON_COLOR};
                border: 1px solid #a0b0e0; /* Active border color */
            }}
            QFrame#nav_button[active="true"]:hover {{
                background-color: {ACTIVE_BUTTON_HOVER_COLOR};
                border: 1px solid #90a0d0; /* Active hover border color */
            }}
        """
        # 應用到所有已創建的按鈕 (如果有的話) 或作為基礎樣式
        # 如果在 _configure_nav_item 中還需要單獨設置其他樣式，需要合併
        # 為了簡單起見，這裡假設此樣式表能覆蓋所有按鈕
        self.setStyleSheet(frame_style) # 應用到 NavigationBar 自身，利用樣式繼承
    # --- 結束新增 ---

    # --- 新增: 設置按鈕活躍狀態的方法 ---
    def set_button_active(self, window_type, is_active):
        """設置指定導航按鈕的活躍狀態並更新樣式"""
        frame = self.button_frames.get(window_type)
        if frame:
            # print(f"Setting {window_type} active: {is_active}") # Debug
            frame.setProperty("active", is_active)
            
            # 刷新樣式以應用屬性變化
            frame.style().unpolish(frame)
            frame.style().polish(frame)
            frame.update() # 確保重繪
    # --- 結束新增 --- 