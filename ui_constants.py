"""
UI 常量定義模塊。

集中管理應用程式界面中使用的各種尺寸、比例、間距、邊距、字體大小等常量。
方便統一修改和維護 UI 外觀。
"""

# ui_constants.py

# 尺寸比例
SIZE_RATIOS = {
    "default": 0.85,
    "small": 0.7,
    "medium": 1.2,
    "large": 1.4
}

# 固定窗口寬度
FIXED_WINDOW_WIDTHS = {
    "default": 125,
    "small": 105,
    "medium": 160,
    "large": 190
}

# 導航欄項目高度附加值
NAV_ITEM_EXTRA_HEIGHT = {
    "default": 25,
    "small": 20,
    "medium": 30,
    "large": 40
}

# 導航欄圖標基礎尺寸
ICON_BASE_SIZE = 68

# 導航欄項目基礎尺寸
NAV_ITEM_BASE_SIZE = 80

# 導航欄字體基礎尺寸
FONT_BASE_SIZE = 10

# (新增) 導航欄按鈕文字大小
NAV_FONT_SIZES = {
    "default": 16,
    "small": 13,
    "medium": 20,
    "large": 23
}

# 導航欄間距
NAV_SPACING = {
    "default": 3,
    "small": 3,
    "medium": 3,
    "large": 3
}

# 導航欄上下邊距
NAV_MARGINS = {
    "default": (10, 10, 10, 2),
    "small":   (10, 8, 10, 2),
    "medium":  (10, 15, 10, 2),
    "large":   (10, 15, 10, 5)
}

# 導航項目內部邊距
NAV_ITEM_MARGINS = {
    "default": (4, 3, 4, 1),
    "small":   (3, 4, 3, 1),
    "medium":  (4, 10, 4, 10),
    "large":   (4, 20, 4, 10)
}

# 導航項目內部間距
NAV_ITEM_SPACING = {
    "default": 2,
    "small": 2,
    "medium": 4,
    "large": 4
}

# 登出按鈕寬度
LOGOUT_BTN_WIDTH = 60

# 出貨輔助標籤高度 (可能不再需要，依賴字體大小)
# SUBSCRIPTION_LABEL_HEIGHT = { ... }

# 登出按鈕高度
LOGOUT_BTN_HEIGHT = {
    "default": 40,
    "small": 30,
    "medium": 36,
    "large": 36
}

# 底部標籤與按鈕間距
BOTTOM_SPACING = {
    "default": 12,
    "small": 10,
    "medium": 20,
    "large": 20
}

# 底部佈局的底部邊距
BOTTOM_LAYOUT_BOTTOM_MARGIN = 10

# (新增) 底部訂閱標籤文字大小
SUBSCRIPTION_FONT_SIZES = {
    "default": 17,
    "small": 14,
    "medium": 21,
    "large": 25
}

# (新增) 導航欄總高度
NAV_HEIGHTS = {
    "default": 500,
    "small": 410,
    "medium": 650,
    "large": 770
} 