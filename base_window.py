"""
基礎內容窗口模塊。

包含 BaseContentWindow 類，作為所有彈出子窗口的基類。
提供統一的窗口框架 (帶標題欄、釘選按鈕、關閉按鈕) 和樣式。
實現基本的窗口拖動和釘選功能。
子類需要在此基礎上構建具體的內容區域。
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                               QFrame, QSizePolicy)
from PySide6.QtCore import Qt, QPoint, QSize
from PySide6.QtGui import QFont, QIcon, QCloseEvent
from PySide6.QtWidgets import QApplication, QMainWindow
from window_behavior_mixin import WindowBehaviorMixin

class BaseContentWindow(QWidget, WindowBehaviorMixin):
    def __init__(self, title, window_type):
        super().__init__()
        self.title = title
        self.window_type = window_type
        self.init_ui()
        self.setup_window_behavior(header_height=30, pin_button=self.pin_button)
        
    def init_ui(self):
        self.setWindowTitle(self.title)
        self.setGeometry(10, 10, 800, 600) # Default size
        self.setWindowFlag(Qt.FramelessWindowHint)
        self.setStyleSheet(""" /* Base window styles */
            QWidget { background-color: #f5f5f5; font-family: Arial; }
            QLabel#title { font-size: 16px; font-weight: bold; color: #ffffff; padding: 2px 5px; margin-left: 2px; background-color: transparent; }
            QPushButton#pin { background-color: transparent; color: #ffffff; border: none; }
            QPushButton#pin[pinned="true"] { color: #ffff99; }
            QPushButton#pin:hover { color: #ccccff; }
            QPushButton#close { background-color: transparent; color: #ffffff; border: none; font-size: 16px; font-weight: bold; padding: 0px; margin: 0px; }
            QPushButton#close:hover { color: #ff9999; }
            QFrame#header { background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #4378de, stop:1 #5ab0b3); border: none; padding: 0px; margin: 0px; }
            QFrame#content { background-color: #ffffff; border: 1px solid #cccccc; }
            /* Specific button/label styles previously here are now handled within their respective page classes or defined globally if needed */
        """)
        
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        header = QFrame()
        header.setObjectName("header")
        header.setFixedHeight(30)
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(10, 0, 10, 0)
        header_layout.setSpacing(2)
        
        self.pin_button = QPushButton()
        self.pin_button.setObjectName("pin")
        self.pin_button.setFixedSize(20, 20)
        self.pin_button.setIcon(QIcon("icons/未釘選.svg"))
        self.pin_button.setIconSize(QSize(16, 16))
        self.pin_button.setProperty("pinned", "false")
        
        title_label = QLabel(self.title)
        title_label.setObjectName("title")
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        
        self.close_button = QPushButton("✕")
        self.close_button.setObjectName("close")
        self.close_button.setFixedSize(18, 18)
        self.close_button.clicked.connect(self.close)
        
        header_layout.addWidget(self.pin_button)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.close_button)
        
        self.content_frame = QFrame()
        self.content_frame.setObjectName("content")
        self.content_layout = QVBoxLayout()
        self.content_layout.setContentsMargins(5, 5, 5, 5)
        self.content_frame.setLayout(self.content_layout)
        
        main_layout.addWidget(header)
        main_layout.addWidget(self.content_frame, 1)
        
        self.setLayout(main_layout)
        
    def closeEvent(self, event: QCloseEvent):
        # Note: The responsibility of saving child window position is now 
        # handled by MainWindow directly before closing the window.
        # This base implementation remains for potential direct use or further subclassing.
        # Removed check for main_window and window_manager as saving is handled centrally.
            
        super().closeEvent(event) 